# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# 虚拟环境
venv/
env/
ENV/
.env

# IDE
.idea/
.vscode/
*.swp
*.swo
.DS_Store

# 数据库
*.db
*.sqlite
*.sqlite3

# 日志
logs/
*.log

# 缓存
.pytest_cache/
.coverage
htmlcov/
.tox/

# 其他
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.production
/Economic_calculation_of_WSHA.xlsx
