version: '3.8'

services:
  app:
    build: .
    container_name: fastapi-app
    ports:
      - "8000:8000"
    volumes:
      - .:/app
    environment:
      - APP_ENV=development
      - DATABASE_URL=sqlite:///./app.db
    command: uvicorn main:app --host 0.0.0.0 --port 8000 --reload
    networks:
      - app-network

  # 如果需要，可以取消注释下面的服务来添加PostgreSQL数据库
  # db:
  #   image: postgres:14
  #   container_name: postgres-db
  #   restart: always
  #   volumes:
  #     - postgres_data:/var/lib/postgresql/data
  #   environment:
  #     - POSTGRES_USER=postgres
  #     - POSTGRES_PASSWORD=postgres
  #     - POSTGRES_DB=app_db
  #   ports:
  #     - "5432:5432"
  #   networks:
  #     - app-network

networks:
  app-network:
    driver: bridge

# volumes:
#   postgres_data: 