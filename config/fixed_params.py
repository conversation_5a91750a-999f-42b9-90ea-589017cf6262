# 固定参数配置
class ProjectParams:
    discount_rate: float = 0  # 贴现率


class ClashFlowParams:
    operation_years = 25  # 运营年限
    construction_years = 2  # 建设年限

    residual_value_rate = 0.05  # 设备残值率5%
    depreciation_rate = 0.95  # 折旧率95%
    depreciation_years = 24  # 折旧年限24年
    depreciation_last_year_value = 0 # 一个待定的值，excel表中是空白的，在单元格C108
    vat_rate_13 = 0.13  # 增值税率13%
    vat_rate_9 = 0.09  # 固定资产增值税率9%
    vat_surcharge_rate = 0.12  # 增值税附加12%
    h2_raw_material_cost_rate = 0.0015  # 制氢原材料成本系数
    h2_water_consumption_rate = 25  # 制氢水耗25吨/吨氢
    h2_wastewater_rate = 15  # 制氢废水15吨/吨氢
    water_price_rate = 0.0004  # 水价0.0004万元/吨
    h2_maintenance_rate = 0.02  # 制氢站维修费用2%
    h2_insurance_rate = 0.002  # 制氢站保险0.2%
    pv_wind_operating_rate = 0.002  # 光伏风电运营管理成本0.2%
    pv_wind_insurance_rate = 0.001  # 光伏风电保险费用0.1%
    electricity_financial_cost_rate = 0.01  # 电力财务成本1%
    h2_personnel_cost = 200  # 制氢站人员支出200万元/年
    h2_other_operating_cost = 200  # 制氢站其他运营管理支出200万元/年
    government_subsidy_params = 0
    # 税收优惠政策参数
    income_tax_free_years = 3  # 所得税免税年限
    income_tax_half_years = 3  # 所得税减半年限
    income_tax_rate = 0.2  # 所得税率20%
    investment_items = [
        "光伏电站", "风电站", "光伏送出线路", "风电送出线路", "升压站", "降压站", "电池储能系统", "制氢工厂",
        "储氢系统", "制氢公辅设施", "合成设备投资", "其他设备投资"
    ]


class EnergyMaterialBalanceParams:
    operation_years = 25  # 运营年限
    construction_years = 2  # 建设年限

    grid_sales_ratio = 0.2  # 上网比例
    loss_ratio = 0.01  # 弃电及网损比例
    transport_h2_ratio = 0.4  # 交通氢能销量比例
    o2_h2_ratio = 8  # 制氧量与制氢量比例
    wind_efficiency_decline_rate = 0.005  # 风电效率年衰减率0.5%
    h2_consumption_increase_rate = 0.01  # 制氢能耗年增长率1%
    h2_equipment_replacement_year = 14  # 制氢设备更换年份


class AmmoniaParams:
    """氨合成项目专用参数"""
    operation_years = 25  # 运营年限
    construction_years = 1  # 建设年限（氨合成项目建设期为第0年）
    
    # 设备残值和折旧参数
    residual_value_rate = 0.05  # 设备残值率5%
    depreciation_years = 24  # 折旧年限24年
    
    # 税率参数
    vat_rate_13 = 0.13  # 增值税率13%（售氨按13%）
    vat_rate_9 = 0.09  # 固定资产增值税率9%
    vat_surcharge_rate = 0.12  # 增值税附加12%
    income_tax_rate = 0.2  # 所得税率20%
    
    # 税收优惠政策参数
    income_tax_free_years = 3  # 所得税免税年限
    income_tax_half_years = 3  # 所得税减半年限
    
    # 维修费用参数
    maintenance_rate = 0.04  # 维修费用率4%（根据md文档表格）
    
    # 原料成本参数（基于md文档表格数据）
    nitrogen_unit_cost = 330  # 外购氮气单价（元/吨）
    nitrogen_annual_consumption = 3  # 氮气年消耗量（吨）
    
    # 催化剂成本参数
    catalyst_unit_cost = 22.88  # 催化剂单价（万元/年）
    catalyst_annual_consumption = 15  # 催化剂年消耗量倍数
    
    # 水费参数
    water_unit_consumption = 10.18  # 单位制氢水耗（吨/年）
    water_consumption_multiplier = 15  # 水耗倍数
    water_price = 0.65  # 水价（元/吨）- 根据MD文档计算
    
    # 制液氨电费参数
    ammonia_electricity_consumption = 760  # 制液氨年电耗（kWh）
    ammonia_electricity_multiplier = 15  # 电耗倍数
    
    # 副产品参数（根据md文档）
    byproduct_multiplier = 15  # 副产品计算倍数
    byproduct_efficiency = 0.913  # 副产品效率系数
    byproduct_price = 200  # 副产品价格，默认值（元/吨）
    
    # 氢气原料成本参数
    hydrogen_consumption_per_ton_ammonia = 11.2  # 制氨氢气单耗（吨氢/吨氨）
    phase2_hydrogen_electricity_coefficient = 1.71  # 二期制氢用电系数
    phase2_hydrogen_electricity_multiplier = 1000  # 二期制氢用电倍数
    hydrogen_price = 20.0  # 氢气价格（元/kg）默认值
    
    # 年产量参数
    ammonia_annual_production = 160000  # 液氨年产量（吨）- 根据md表格
    
    # 人员参数
    personnel_cost = 10  # 人员单价（万元/人·年）
    personnel_multiplier = 20  # 人员数量倍数
    
    # 其他运营管理支出
    other_operating_cost = 500  # 其他运营管理支出（万元/年）
    
    # 电价参数
    electricity_price = 0.23  # 电价（元/kWh）

project_params = ProjectParams()
clash_flow_params = ClashFlowParams()
energy_material_balance_params = EnergyMaterialBalanceParams()
ammonia_params = AmmoniaParams()
# 默认单位造价 修改时需同步修改规模映射表
default_unit_costs = {
    "光伏电站": {"scale_unit": "MW", "unit_cost": 400, "unit": "万元/MW"},
    "风电站": {"scale_unit": "MW", "unit_cost": 550, "unit": "万元/MW"},
    "光伏送出线路": {"scale_unit": "km", "unit_cost": 80, "unit": "万元/km"},
    "风电送出线路": {"scale_unit": "km", "unit_cost": 80, "unit": "万元/km"},
    "升压站": {"scale_unit": "MW", "unit_cost": 35, "unit": "万元/MW"},
    "降压站": {"scale_unit": "MW", "unit_cost": 35, "unit": "万元/MW"},
    "电池储能系统": {"scale_unit": "MW", "unit_cost": 80, "unit": "万元/MW"},
    "制氢工厂": {"scale_unit": "MW", "unit_cost": 330, "unit": "万元/MW"},
    "储氢系统": {"scale_unit": "t", "unit_cost": 5, "unit": "万元/t"},
    "制氢公辅设施": {"scale_unit": "", "unit_cost": 0, "unit": "", "total_investment": 15000},
    "其他1": {"scale_unit": "", "unit_cost": 0, "unit": "", "total_investment": 10},
    "其他2": {"scale_unit": "", "unit_cost": 0, "unit": "", "total_investment": 10},
    "其他3": {"scale_unit": "", "unit_cost": 0, "unit": "", "total_investment": 10},
    "其他4": {"scale_unit": "", "unit_cost": 0, "unit": "", "total_investment": 10},
    "其他5": {"scale_unit": "", "unit_cost": 0, "unit": "", "total_investment": 10}
}
