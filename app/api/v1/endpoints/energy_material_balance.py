from typing import List
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.db.session import get_db
from app.schemas.response import StandardResponse
from app.services.energy_material_balance_service import EnergyMaterialBalanceService

router = APIRouter()


@router.get("/project/{project_id}", response_model=StandardResponse[List[dict]])
async def get_energy_material_balance_by_project(
    project_id: int,
    db: AsyncSession = Depends(get_db)
):
    """
    获取指定项目的所有能量物质平衡记录

    Args:
        project_id: 项目ID
        db: 数据库会话

    Returns:
        能量物质平衡记录列表
    """
    try:
        service = EnergyMaterialBalanceService(db)
        records = await service.get_by_project_id(project_id)

        return StandardResponse[List[dict]](
            data=records,
            code=200,
            message=f"成功获取项目 {project_id} 的能量物质平衡记录，共 {len(records)} 条"
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取能量物质平衡记录失败: {str(e)}"
        )


