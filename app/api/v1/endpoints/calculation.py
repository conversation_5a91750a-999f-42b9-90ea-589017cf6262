from fastapi import APIRouter, HTTPException, status, Depends
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession

from app.db.session import get_db
from app.schemas.calculation import CalculationRequest, CalculationResponse
from app.schemas.response import StandardResponse
from app.services.calculation_service import CalculationService

router = APIRouter()


@router.post("", response_model=CalculationResponse, status_code=status.HTTP_200_OK)
async def process_calculation(
    calculation_request: CalculationRequest,
    project_name: str=None,
    db: AsyncSession = Depends(get_db)
):
    """
    处理计算请求

    接收包含多个模块数据的JSON请求，进行计算处理并返回结果
    """
    try:
        # 创建计算服务实例（传入数据库会话）
        calculation_service = CalculationService(db)

        # 验证输入数据
        if not await calculation_service.validate_calculation_request(calculation_request):
            return CalculationResponse(
                data=None,
                code=400,
                message="无效的计算请求数据"
            )

        # 处理计算
        result = await calculation_service.process_calculation(calculation_request,project_name)

        # 构建成功响应
        return CalculationResponse(
            data=result,
            code=200,
            message="计算处理完成"
        )

    except ValueError as e:
        # 处理参数错误
        return CalculationResponse(
            data=None,
            code=400,
            message=f"参数错误: {str(e)}"
        )
    except Exception as e:
        # 处理其他异常
        return CalculationResponse(
            data=None,
            code=500,
            message=f"计算处理失败: {str(e)}"
        )


@router.post("/validate", response_model=StandardResponse[dict], status_code=status.HTTP_200_OK)
async def validate_calculation_data(
    calculation_request: CalculationRequest
):
    """
    验证计算数据格式

    用于前端在提交前验证数据格式是否正确
    """
    try:
        calculation_service = CalculationService()

        is_valid = await calculation_service.validate_calculation_request(calculation_request)

        validation_data = {
            "valid": is_valid,
            "photovoltaic_capacity_mw": calculation_request.photovoltaic.pv_capacity_mw,
            "wind_capacity_mw": calculation_request.windPower.wind_capacity_mw,
            "hydrogen_capacity_mw": calculation_request.hydrogenPlant.h2_plant_capacity_mw,
            "timestamp": datetime.now().isoformat()
        }

        return StandardResponse[dict](
            data=validation_data,
            code=200,
            message="验证完成"
        )

    except Exception as e:
        return StandardResponse[dict](
            data=None,
            code=500,
            message=f"验证失败: {str(e)}"
        )
