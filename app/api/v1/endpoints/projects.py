from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.db.session import get_db
from app.schemas.response import StandardResponse
from app.services.project_query_service import ProjectQueryService

router = APIRouter()


@router.get("/id/{project_id}", response_model=StandardResponse)
async def get_project_complete_data(
    project_id: int,
    db: AsyncSession = Depends(get_db)
):
    """
    获取指定项目的完整数据
    
    通过project_id查询项目的所有相关数据，包括：
    - 项目基本信息
    - 运营参数
    - 固定投资项列表
    - 能量物质平衡记录
    - 现金流记录
    """
    try:
        project_service = ProjectQueryService(db)
        project_data = await project_service.get_project_complete_data(project_id)
        
        if project_data is None:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"项目 ID {project_id} 不存在"
            )
        
        return StandardResponse(
            data=project_data,
            code=200,
            message="项目数据获取成功"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取项目数据时发生错误: {str(e)}"
        )

@router.get("/name/{project_name}",response_model=StandardResponse)
async def get_project_complete_data_by_name(
    project_name: str,
    db: AsyncSession = Depends(get_db)
):
    """
    通过项目名称获取项目的所有相关数据
    """
    try:
        project_service = ProjectQueryService(db)
        project_data = await project_service.get_project_complete_data_by_name(project_name)

        if project_data is None:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"项目名称 {project_name} 不存在"
            )

        return StandardResponse(
            data=project_data,
            code=200,
            message="项目数据获取成功"
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取项目数据时发生错误: {str(e)}"
        )
@router.get("", response_model=StandardResponse)
async def get_projects_list(
    skip: int = 0,
    limit: int = 100,
    db: AsyncSession = Depends(get_db)
):
    """
    获取项目列表
    
    返回项目的基本信息和摘要统计，支持分页查询
    """
    try:
        # 验证分页参数
        if skip < 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="skip参数不能小于0"
            )
        
        if limit <= 0 or limit > 1000:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="limit参数必须在1-1000之间"
            )
        
        project_service = ProjectQueryService(db)
        projects_data = await project_service.get_projects_list(skip, limit)

        
        return StandardResponse(
            data={
                "projects": projects_data,
                "total": len(projects_data)
            },
            code=200,
            message=f"成功获取 {len(projects_data)} 个项目"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取项目列表时发生错误: {str(e)}"
        )


@router.delete("/{project_id}", response_model=StandardResponse)
async def delete_project(
    project_id: int,
    db: AsyncSession = Depends(get_db)
):
    """
    删除项目及其所有关联数据
    
    注意：此操作将删除项目的所有相关数据，包括运营参数、固定投资、
    能量物质平衡记录和现金流记录。此操作不可逆。
    """
    try:
        project_service = ProjectQueryService(db)
        
        # 首先检查项目是否存在
        project_data = await project_service.get_project_complete_data(project_id)
        if project_data is None:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"项目 ID {project_id} 不存在"
            )
        
        # 删除项目（由于设置了cascade="all, delete-orphan"，关联数据会自动删除）
        deleted_project = await project_service.project_repo.delete(project_id)
        
        if deleted_project is None:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="删除项目失败"
            )
        
        return StandardResponse[dict](
            data={
                "deleted_project_id": project_id,
                "deleted_project_name": project_data["project_info"]["name"]
            },
            code=200,
            message=f"项目 '{project_data['project_info']['name']}' 及其所有关联数据已成功删除"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除项目时发生错误: {str(e)}"
        )
