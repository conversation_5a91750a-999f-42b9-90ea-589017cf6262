from typing import List
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.db.session import get_db
from app.schemas.response import StandardResponse
from app.services.fixed_investment_service import FixedInvestmentService

router = APIRouter()


@router.get("/project/{project_id}", response_model=StandardResponse[List[dict]])
async def get_fixed_investments_by_project(
    project_id: int,
    db: AsyncSession = Depends(get_db)
):
    """
    获取指定项目的所有固定投资项

    Args:
        project_id: 项目ID
        db: 数据库会话

    Returns:
        固定投资项列表
    """
    try:
        service = FixedInvestmentService(db)
        items = await service.get_by_project_id(project_id)

        return StandardResponse[List[dict]](
            data=items,
            code=200,
            message=f"成功获取项目 {project_id} 的固定投资项，共 {len(items)} 项"
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取固定投资项失败: {str(e)}"
        )
