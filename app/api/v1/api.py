from fastapi import APIRouter

from app.api.v1.endpoints import health, users, items, auth, calculation, projects, fixed_investments, energy_material_balance, cash_flow

# 创建API路由器
api_router = APIRouter()

# 注册各个端点路由
api_router.include_router(health.router, prefix="/health", tags=["health"])
api_router.include_router(auth.router, prefix="/auth", tags=["auth"])
api_router.include_router(users.router, prefix="/users", tags=["users"])
api_router.include_router(items.router, prefix="/items", tags=["items"])
api_router.include_router(calculation.router, prefix="/calculation", tags=["calculation"])
api_router.include_router(projects.router, prefix="/projects", tags=["projects"])
api_router.include_router(fixed_investments.router, prefix="/fixed-investments", tags=["fixed-investments"])
api_router.include_router(energy_material_balance.router, prefix="/energy-material-balance", tags=["energy-material-balance"])
api_router.include_router(cash_flow.router, prefix="/cash-flow", tags=["cash-flow"])