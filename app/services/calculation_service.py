from typing import Dict, Any, List
from datetime import datetime
import uuid
from sqlalchemy.ext.asyncio import AsyncSession

from app.services.calculator.cash_flow_for_ammonia_calculator import CashFlowForAmmoniaCalculator
from config import project_params
from app.schemas.calculation import CalculationRequest, CalculationResult
from app.utils.transaction import transactional

from app.services.calculator.cash_flow_calculator import CashFlowCalculator
from app.services.data_conversion_service import DataConversionService
from app.services.calculator.fixed_investment_calculator import FixedInvestmentCalculator
from app.services.calculator.energy_material_balance_calculator import EnergyMaterialBalanceCalculator
from app.db.repositories.project_repository import ProjectRepository
from app.db.repositories.operating_params_repository import OperatingParamsRepository
from app.db.repositories.fixed_investment_repository import FixedInvestmentRepository
from app.db.repositories.energy_material_balance_repository import EnergyMaterialBalanceRepository
from app.db.repositories.cash_flow_repository import CashFlowRepository


class CalculationService:
    """计算服务类，处理计算相关业务逻辑"""

    def __init__(self, db: AsyncSession = None):
        self.db = db
        self.operating_params_converter = DataConversionService()
        self.fixed_investment_calculator = FixedInvestmentCalculator()
        self.energy_material_balance_calculator = EnergyMaterialBalanceCalculator()
        self.cash_flow_calculator = CashFlowCalculator()
        self.cash_flow_for_ammonia_calculator = CashFlowForAmmoniaCalculator()
        # 初始化repositories（仅在有数据库会话时）
        if self.db:
            self.project_repo = ProjectRepository(self.db)
            self.operating_params_repo = OperatingParamsRepository(self.db)
            self.fixed_investment_repo = FixedInvestmentRepository(self.db)
            self.energy_material_balance_repo = EnergyMaterialBalanceRepository(self.db)
            self.cash_flow_repo = CashFlowRepository(self.db)
    
    @transactional()
    async def process_calculation(self, calculation_request: CalculationRequest,project_name: str = None) -> CalculationResult:
        """
        处理计算请求

        Args:
            calculation_request: 计算请求数据

        Returns:
            CalculationResult: 计算结果

        Raises:
            Exception: 计算过程中的任何异常都会向上抛出，由API层处理
        """
        # 生成结果ID
        result_id = str(uuid.uuid4())
        if project_name is None or project_name =='':
            project_name = f"经济测算项目_{datetime.now().strftime('%Y%m%d-%H:%M')}"
        project = await self.project_repo.create_no_commit(name=project_name) # 创建项目，获取项目id

        # 1. 转换数据为运营参数
        project.operating_params = self.operating_params_converter.convert_to_operating_params(calculation_request)
        project.operating_params.project_id = project.id
        await self.operating_params_repo.create_from_project_no_commit(project.operating_params)

        # 2. 执行固定投资计算
        fixed_investment_result = self.fixed_investment_calculator.calculate_project_fixed_investment(project.operating_params)
        for item in fixed_investment_result:
            item.project_id = project.id
        await self.fixed_investment_repo.create_batch_no_commit(fixed_investment_result)

        # 3. 计算能量物质平衡表
        energy_material_balance_result = self.energy_material_balance_calculator.calculate_energy_material_balance(project.operating_params)
        for record in energy_material_balance_result:
            record.project_id = project.id
        await self.energy_material_balance_repo.create_batch_no_commit(energy_material_balance_result)

        #4. 计算现金流量表
        cash_flow_result,project_payback_period = self.cash_flow_calculator.calculate_cash_flow(
            project.operating_params, 
            energy_material_balance_result, 
            fixed_investment_result
        )

        # 5. 计算合成氨
        ammonia_result = self.cash_flow_for_ammonia_calculator.calculate_cash_flow(
            project.operating_params,
            energy_material_balance_result,
            fixed_investment_result
        )
        project.payback_period = project_payback_period
        # 计算 IRR 和 NPV（使用现金流数据而不是访问关系属性）
        self._calculate_project_metrics(project, cash_flow_result)

        for record in cash_flow_result:
            record.project_id = project.id
        await self.cash_flow_repo.create_batch_no_commit(cash_flow_result)

        # 更新项目的IRR和NPV
        await self.project_repo.update_no_commit(project.id,
            irr_before_tax=project.irr_before_tax,
            npv_before_tax=project.npv_before_tax,
            irr_after_tax=project.irr_after_tax,
            npv_after_tax=project.npv_after_tax,
            project_payback_period = project.payback_period
        )

        # 返回纯计算结果，不包含状态信息
        return CalculationResult(
            result_id=result_id,
            results={
                "project_id": project.id,
                "project_name": project_name,
                "payback_period": project.payback_period,
                "npv_before_tax": project.npv_before_tax,
                "npv_after_tax": project.npv_after_tax,
                "irr_before_tax": project.irr_before_tax,
                "irr_after_tax": project.irr_after_tax
            },
            timestamp=datetime.now()
        )
    
    def _calculate_project_metrics(self, project, cash_flow_records):
        """计算项目的IRR和NPV指标"""
        import numpy_financial as npf
        
        # 提取现金流数据
        cash_flows_before_tax = [record.net_cash_flow_before_tax for record in cash_flow_records if record.year != 0]
        cash_flows_after_tax = [record.net_cash_flow_after_tax for record in cash_flow_records if record.year != 0]
        
        # 计算IRR
        project.irr_before_tax = npf.irr(cash_flows_before_tax)
        project.irr_after_tax = npf.irr(cash_flows_after_tax)
        
        # 计算NPV
        cash_flows_before_tax_operation = [record.net_cash_flow_before_tax for record in cash_flow_records if record.year > 0]
        cash_flows_after_tax_operation = [record.net_cash_flow_after_tax for record in cash_flow_records if record.year > 0]
        
        # 查找year=-1的记录
        initial_investment_record = next((record for record in cash_flow_records if record.year == -1), None)
        initial_investment = initial_investment_record.net_cash_flow_before_tax if initial_investment_record else 0
        
        project.npv_before_tax = npf.npv(project_params.discount_rate, cash_flows_before_tax_operation) + initial_investment
        project.npv_after_tax = npf.npv(project_params.discount_rate, cash_flows_after_tax_operation) + initial_investment
    
    async def validate_calculation_request(self, calculation_request: CalculationRequest) -> bool:
        """
        验证计算请求数据的有效性

        Args:
            calculation_request: 计算请求

        Returns:
            bool: 验证结果
        """
        try:
            # 使用数据转换服务进行验证
            is_valid, _ = self.operating_params_converter.validate_conversion_data(calculation_request)
            return is_valid
        except Exception:
            return False

