from datetime import datetime
from typing import Dict, Any, Optional
from app.schemas.calculation import CalculationRequest
from app.db.models.project import Project
from app.db.models.operating_params import OperatingParams
from app.db.models.fixed_investment_item import FixedInvestmentItem


class DataConversionService:
    """数据转换服务，将前端JSON数据转换为数据库实体"""

    def __init__(self):
        pass


    def convert_to_operating_params(self, request: CalculationRequest) -> OperatingParams:
        """转换为运营参数"""
        pv = request.photovoltaic
        wind = request.windPower
        grid = request.gridSystem
        h2 = request.hydrogenPlant
        financing = request.financing
        ammonia = request.liquidAmmonia
        energy_storage = request.energyStoragePowerStation

        return OperatingParams(
            # 光伏参数
            pv_capacity_mw=pv.pv_capacity_mw,
            pv_utilization_hours=pv.pv_utilization_hours,
            pv_degradation_y1_percent=pv.pv_degradation_y1_percent,
            pv_degradation_ongoing_percent=pv.pv_degradation_ongoing_percent,
            pv_system_efficiency_percent=pv.system_efficiency_percent,
            pv_transmission_distance_km=pv.pv_transmission_distance_km,
            pv_equipment_depreciation_years=pv.equipment_depreciation_years,
            pv_grid_price_tax_included_yuan_per_kwh=pv.grid_price_tax_included_yuan_per_kwh,

            # 风电参数
            wind_capacity_mw=wind.wind_capacity_mw,
            wind_utilization_hours=wind.wind_utilization_hours,
            wind_system_efficiency_percent=wind.system_efficiency_percent,
            wind_transmission_distance_km=wind.wind_transmission_distance_km,
            wind_equipment_depreciation_years=wind.equipment_depreciation_years,
            grid_price_tax_included_yuan_per_kwh=wind.grid_price_tax_included_yuan_per_kwh,

            # 电网系统参数
            grid_transmission_length_km=grid.grid_transmission_length_km,
            grid_transmission_unit_cost_wan_yuan_per_km=grid.grid_transmission_unit_cost_wan_yuan_per_km,
            grid_step_up_station_capacity_mva=grid.grid_step_up_station_capacity_mva,
            grid_step_up_station_unit_cost_wan_yuan_per_mva=grid.grid_step_up_station_unit_cost_wan_yuan_per_mva,
            grid_step_down_station_capacity_mva=grid.grid_step_down_station_capacity_mva,
            grid_step_down_station_unit_cost_wan_yuan_per_mva=grid.grid_step_down_station_unit_cost_wan_yuan_per_mva,

            # 制氢参数
            h2_plant_capacity_mw=h2.h2_plant_capacity_mw,
            h2_consumption_kwh_per_nm3=h2.h2_consumption_kwh_per_nm3,
            h2_base_consumption_kwh_per_kg=h2.h2_base_consumption_kwh_per_kg,
            h2_equipment_service_years=h2.h2_equipment_service_years,
            h2_consumption_increase_annual=h2.h2_consumption_increase_annual,
            h2_water_price_yuan_per_ton=h2.h2_water_price_yuan_per_ton,
            h2_wastewater_price_yuan_per_ton=h2.h2_wastewater_price_yuan_per_ton,
            h2_staff_count=h2.h2_staff_count,
            h2_staff_salary_wan_yuan_per_year=h2.h2_staff_salary_wan_yuan_per_year,
            h2_price_transport=h2.h2_price_transport,
            h2_price_chemical=h2.h2_price_chemical,
            h2_storage_investment_wan_yuan=h2.h2_storage_investment_wan_yuan,
            h2_storage_capacity_tons=h2.h2_storage_capacity_tons,
            h2_equipment_depreciation_years=h2.equipment_depreciation_years,
            o2_price_per_ton=h2.o2_price_per_ton,

            # 融资参数
            loan_term_years=financing.loan_term_years,
            loan_interest_rate=financing.loan_interest_rate_percent / 100,  # 转换为小数
            finance_land_rent_wan_yuan_per_year=financing.finance_land_rent_wan_yuan_per_year,
            loan_ratio=financing.loan_ratio,
            finance_loan_total_wan_yuan=financing.finance_loan_total_wan_yuan,

            # 液氨参数
            ammonia_price_yuan_per_ton=ammonia.ammonia_price_yuan_per_ton,
            ammonia_consumption_tons_per_hour=ammonia.ammonia_consumption_tons_per_hour,

            # 储能电站参数
            electrochemical_energy_storage_scale_mw=energy_storage.electrochemical_energy_storage_scale_mw,
        )



    def validate_conversion_data(self, request: CalculationRequest) -> tuple[bool, str]:
        """
        验证转换数据的有效性
        
        Returns:
            tuple: (是否有效, 错误消息)
        """
        try:
            # 检查必要的数值是否为正数
            if request.photovoltaic.pv_capacity_mw <= 0:
                return False, "光伏装机容量必须大于0"
            
            if request.windPower.wind_capacity_mw <= 0:
                return False, "风电装机容量必须大于0"
            
            if request.hydrogenPlant.h2_plant_capacity_mw <= 0:
                return False, "制氢容量必须大于0"
            
            # 检查百分比数值范围
            if not (0 <= request.photovoltaic.system_efficiency_percent <= 100):
                return False, "光伏系统效率必须在0-100%之间"
            
            if not (0 <= request.windPower.system_efficiency_percent <= 100):
                return False, "风电系统效率必须在0-100%之间"
            
            # 检查贷款比例
            if not (0 <= request.financing.loan_ratio <= 1):
                return False, "贷款比例必须在0-1之间"
            
            return True, "验证通过"
            
        except Exception as e:
            return False, f"数据验证失败: {str(e)}"
