from typing import List, Dict

from app.db.models import CashFlowForAmmonia, EnergyMaterialBalance, FixedInvestmentItem, OperatingParams
from app.utils.calculate_utils import round_amount
from config.fixed_params import ammonia_params


class CashFlowForAmmoniaCalculator:
    """氨合成项目现金流量计算器，根据氨合成分析表的逻辑计算1+0+25年的现金流量数据"""

    def __init__(self):
        self.operation_years = ammonia_params.operation_years  # 运营年限25年
        self.construction_years = ammonia_params.construction_years  # 建设年限1年（第0年）

        # 固定参数
        self.residual_value_rate = ammonia_params.residual_value_rate  # 设备残值率5%
        self.depreciation_years = ammonia_params.depreciation_years  # 折旧年限24年
        self.vat_rate_13 = ammonia_params.vat_rate_13  # 增值税率13%
        self.vat_rate_9 = ammonia_params.vat_rate_9  # 固定资产增值税率9%
        self.vat_surcharge_rate = ammonia_params.vat_surcharge_rate  # 增值税附加12%
        self.maintenance_rate = ammonia_params.maintenance_rate  # 维修费用4%
        # 原料成本参数
        self.nitrogen_unit_cost = ammonia_params.nitrogen_unit_cost  # 氮气单价
        self.nitrogen_annual_consumption = ammonia_params.nitrogen_annual_consumption  # 氮气年消耗量
        
        # 催化剂成本参数
        self.catalyst_unit_cost = ammonia_params.catalyst_unit_cost  # 催化剂单价
        self.catalyst_annual_consumption = ammonia_params.catalyst_annual_consumption  # 催化剂年消耗量倍数
        
        # 水费参数
        self.water_unit_consumption = ammonia_params.water_unit_consumption  # 单位制氢水耗
        self.water_consumption_multiplier = ammonia_params.water_consumption_multiplier  # 水耗倍数
        self.water_price = ammonia_params.water_price  # 水价
        
        # 制液氨电费参数
        self.ammonia_electricity_consumption = ammonia_params.ammonia_electricity_consumption  # 制液氨电耗
        self.ammonia_electricity_multiplier = ammonia_params.ammonia_electricity_multiplier  # 电耗倍数
        
        # 副产品参数
        self.byproduct_multiplier = ammonia_params.byproduct_multiplier  # 副产品倍数
        self.byproduct_efficiency = ammonia_params.byproduct_efficiency  # 副产品效率
        self.byproduct_price = ammonia_params.byproduct_price  # 副产品价格
        
        # 氢气原料参数
        self.hydrogen_consumption_per_ton_ammonia = ammonia_params.hydrogen_consumption_per_ton_ammonia  # 制氨氢气单耗
        self.phase2_hydrogen_electricity_coefficient = ammonia_params.phase2_hydrogen_electricity_coefficient  # 二期制氢用电系数
        self.phase2_hydrogen_electricity_multiplier = ammonia_params.phase2_hydrogen_electricity_multiplier  # 二期制氢用电倍数
        self.hydrogen_price = ammonia_params.hydrogen_price  # 氢气价格
        
        # 年产量参数
        self.ammonia_annual_production = ammonia_params.ammonia_annual_production  # 液氨年产量
        
        # 人员参数
        self.personnel_cost = ammonia_params.personnel_cost  # 人员单价
        self.personnel_multiplier = ammonia_params.personnel_multiplier  # 人员数量倍数
        
        # 其他运营管理支出
        self.other_operating_cost = ammonia_params.other_operating_cost  # 其他运营管理支出
        
        # 电价参数
        self.electricity_price = ammonia_params.electricity_price  # 电价
        
        # 税收优惠政策参数
        self.income_tax_free_years = ammonia_params.income_tax_free_years  # 所得税免税年限
        self.income_tax_half_years = ammonia_params.income_tax_half_years  # 所得税减半年限
        self.income_tax_rate = ammonia_params.income_tax_rate  # 所得税率20%

        # 合成氨投资,运行时计算
        self.ammonia_scale = 0.0
    def calculate_cash_flow(self, operating_params:OperatingParams, energy_material_balance_records, fixed_investment_items) -> List[CashFlowForAmmonia]:
        """计算氨合成项目现金流量表"""
        cash_flow_records: List[CashFlowForAmmonia] = []

        if not operating_params:
            raise ValueError("缺少运营参数")
        if not fixed_investment_items:
            raise ValueError("缺少固定投资数据")

        # 获取基础数据
        energy_balance_records = {record.year: record for record in energy_material_balance_records} if energy_material_balance_records else {}
        fixed_investments = {item.item_name: item for item in fixed_investment_items}
        # 计算运行时参数
        self.ammonia_scale = operating_params.ammonia_consumption_tons_per_hour*0.8# 计算合成氨规模
        # 计算建设期数据（年份0）
        record = self._calculate_period_record(
            None, 0, operating_params, energy_balance_records, fixed_investments
        )
        cash_flow_records.append(record)

        # 计算运营期数据（年份1-25）
        for year in range(1, self.operation_years + 1):
            record = self._calculate_period_record(
                None, year, operating_params, energy_balance_records, fixed_investments, cash_flow_records[-1]
            )
            cash_flow_records.append(record)

        # 计算合计数据（年份-1）
        total_record = self._calculate_total_record(None, cash_flow_records)
        cash_flow_records.insert(0, total_record)  # 插入到开头

        return cash_flow_records

    def _calculate_period_record(
        self, project_id: int, year: int, operating_params: OperatingParams,
        energy_balance_records: Dict[int, EnergyMaterialBalance], fixed_investments: Dict[str, FixedInvestmentItem],
        last_record: CashFlowForAmmonia = None
    ) -> CashFlowForAmmonia:
        """计算任意年份的记录（建设期或运营期）"""

        # 创建基础记录
        record = CashFlowForAmmonia(project_id=project_id, year=year)

        # === 1. 计算现金流入 ===
        if year > 0:  # 运营期才有收入
            # 1.1 售液氨收入 = 合成氨产量 * 售氨价格 / 10000（转换为万元）

            ammonia_price = self.ammonia_scale*10000
            record.ammonia_sales_income = round_amount(self.ammonia_annual_production * ammonia_price / 10000)
            
            # 1.2 副产品收入
            byproduct_price = getattr(operating_params, 'byproduct_price', self.byproduct_price)  # 使用参数或默认值
            record.byproduct_income = round_amount(self.byproduct_multiplier * (self.byproduct_efficiency * byproduct_price) / 10000)
            
            # 1.3 设备残值（5%）（仅第25年）
            if year == 25:
                total_investment = sum([item.total_investment or 0 for item in fixed_investments.values()])
                record.equipment_residual_value = round_amount(total_investment * self.residual_value_rate)
            
            # 1.4 政府直接补贴（目前为0）
            record.government_subsidy = 0.0

        # 计算现金流入合计
        record.total_cash_inflow = round_amount(
            (record.ammonia_sales_income or 0) + (record.byproduct_income or 0) + 
            (record.equipment_residual_value or 0) + (record.government_subsidy or 0)
        )

        # === 2. 计算现金流出 ===
        if year == 0:  # 建设期第0年进行固定投资
            # 2.1 制氢建设投资
            total_investment = sum([item.total_investment or 0 for item in fixed_investments.values()])
            record.hydrogen_plant_investment = round_amount(total_investment)

        if year > 0:  # 运营期才有运营成本
            # 2.21 外购氮气原料
            record.nitrogen_raw_material_cost = round_amount(self.nitrogen_unit_cost * self.nitrogen_annual_consumption / 10000)
            
            # 2.22 外购1期氢气原料
            hydrogen_price = getattr(operating_params, 'h2_price_transport', self.hydrogen_price)  # 使用OperatingParams中的氢气价格或默认值
            record.hydrogen_raw_material_cost = round_amount(hydrogen_price * 1000 * self.hydrogen_consumption_per_ton_ammonia / 10000)
            
            # 2.23 二期制氢用电成本
            electricity_price = getattr(operating_params, 'electricity_price', self.electricity_price)  # 使用参数或默认值
            record.phase2_hydrogen_electricity_cost = round_amount(
                (self.phase2_hydrogen_electricity_coefficient * self.hydrogen_consumption_per_ton_ammonia * 
                 self.phase2_hydrogen_electricity_multiplier * electricity_price) / 10000
            )
            
            # 2.3 维修费用（4%）
            total_investment = sum([item.total_investment or 0 for item in fixed_investments.values()])
            record.maintenance_cost = round_amount(self.maintenance_rate * total_investment)
            
            # 2.4 人员支出
            personnel_cost = getattr(operating_params, 'h2_staff_salary_wan_yuan_per_year', self.personnel_cost)  # 使用OperatingParams或默认值
            personnel_count = getattr(operating_params, 'h2_staff_count', self.personnel_multiplier)  # 使用OperatingParams或默认值
            record.personnel_cost = round_amount(personnel_cost * personnel_count)
            
            # 2.5 其他运营管理支出
            record.other_operating_cost = round_amount(self.other_operating_cost)
            
            # 2.6 水费
            water_price = getattr(operating_params, 'h2_water_price_yuan_per_ton', self.water_price)  # 使用OperatingParams或默认值
            record.water_cost = round_amount(self.water_unit_consumption * water_price * self.water_consumption_multiplier / 10000)
            
            # 2.8 制液氨电费
            record.ammonia_electricity_cost = round_amount(self.ammonia_electricity_consumption * electricity_price * self.ammonia_electricity_multiplier / 10000)
            
            # 2.7 催化剂
            record.catalyst_cost = round_amount(self.catalyst_unit_cost * self.catalyst_annual_consumption)
            
            # 2.8 财务成本（目前为0）
            record.financial_cost = 0.0

        # 计算现金流出合计
        record.total_cash_outflow = round_amount(
            (record.hydrogen_plant_investment or 0) + (record.nitrogen_raw_material_cost or 0) +
            (record.hydrogen_raw_material_cost or 0) + (record.phase2_hydrogen_electricity_cost or 0) +
            (record.maintenance_cost or 0) + (record.personnel_cost or 0) +
            (record.other_operating_cost or 0) + (record.water_cost or 0) +
            (record.ammonia_electricity_cost or 0) + (record.catalyst_cost or 0) +
            (record.financial_cost or 0)
        )

        # === 3-4. 净现金流量 ===
        record.net_cash_flow_before_tax = round_amount((record.total_cash_inflow or 0) - (record.total_cash_outflow or 0))

        # 累计净现金流量（税前）
        if year == 0:  # 建设期
            record.cumulative_net_cash_flow_before_tax = round_amount(record.net_cash_flow_before_tax)
        else:  # 运营期
            if last_record:
                record.cumulative_net_cash_flow_before_tax = round_amount(
                    last_record.cumulative_net_cash_flow_before_tax + record.net_cash_flow_before_tax
                )
            else:
                record.cumulative_net_cash_flow_before_tax = round_amount(record.net_cash_flow_before_tax)

        # === 5. 折旧 ===
        if year > 0:  # 运营期才有折旧
            total_investment = sum([item.total_investment or 0 for item in fixed_investments.values()])
            # 按不含税投资额计算折旧
            record.depreciation = round_amount((total_investment / 1.09) / self.depreciation_years)

        # === 6. 税务计算 ===
        if year > 0:  # 运营期才有税务计算
            # 6.1 增值税销项（售氨按13%）
            if record.total_cash_inflow > 0:
                record.vat_output = round_amount(self.vat_rate_13 * record.total_cash_inflow / (1 + self.vat_rate_13))
            
            # 6.2 增值税进项（一）（运行成本按13%）
            if record.total_cash_outflow > 0:
                record.vat_input_operating = round_amount(0.13 * (record.total_cash_outflow / 1.13))
            
            # 6.3 增值税进项（二）（固定资产按9%）
            total_investment = sum([item.total_investment or 0 for item in fixed_investments.values()])
            record.vat_input_fixed_assets = round_amount(0.09 * total_investment / (1 + 0.09))
            
            # 6.4 缴纳增值税
            record.vat_payable = round_amount((record.vat_output or 0) - (record.vat_input_operating or 0) - (record.vat_input_fixed_assets or 0))
            
            # 6.5 缴纳增值税附加
            record.vat_surcharge = round_amount((record.vat_payable or 0) * self.vat_surcharge_rate)
            
            # 6.6 增值税及附加总额
            record.total_vat_and_surcharge = round_amount((record.vat_payable or 0) + (record.vat_surcharge or 0))

        # === 7. 所得税（三免三减半） ===
        if year > 0:
            record.income_tax = round_amount(self._calculate_income_tax(year, record.net_cash_flow_before_tax, record.depreciation, record.total_vat_and_surcharge))

        # === 8-9. 税后现金流 ===
        record.net_cash_flow_after_tax = round_amount(
            (record.net_cash_flow_before_tax or 0) - (record.income_tax or 0) - (record.total_vat_and_surcharge or 0)
        )

        # 累计现金流（税后）
        if year == 0:  # 建设期
            record.cumulative_net_cash_flow_after_tax = round_amount(record.net_cash_flow_after_tax)
        else:  # 运营期
            if last_record:
                record.cumulative_net_cash_flow_after_tax = round_amount(
                    last_record.cumulative_net_cash_flow_after_tax + record.net_cash_flow_after_tax
                )
            else:
                record.cumulative_net_cash_flow_after_tax = round_amount(record.net_cash_flow_after_tax)

        return record

    def _calculate_income_tax(self, year: int, net_cash_flow_before_tax: float, depreciation: float, total_vat_and_surcharge: float) -> float:
        """计算所得税（按三免三减半政策）"""
        if year <= 0:  # 建设期不征收所得税
            return 0.0
        elif year <= self.income_tax_free_years:
            return 0.0  # 前3年免税
        elif year <= self.income_tax_free_years + self.income_tax_half_years:
            # 4-6年减半征收
            if net_cash_flow_before_tax > 0 and (net_cash_flow_before_tax - depreciation - total_vat_and_surcharge) > 0:
                return (net_cash_flow_before_tax - depreciation - total_vat_and_surcharge) * self.income_tax_rate * 0.5
            else:
                return 0.0
        else:
            # 7-25年全额征收
            if net_cash_flow_before_tax > 0 and (net_cash_flow_before_tax - depreciation - total_vat_and_surcharge) > 0:
                return (net_cash_flow_before_tax - depreciation - total_vat_and_surcharge) * self.income_tax_rate
            else:
                return 0.0

    def _calculate_total_record(self, project_id: int, records: List[CashFlowForAmmonia]) -> CashFlowForAmmonia:
        """计算合计记录（年份-1）"""

        # 汇总所有年份的数据
        total_fields = {}

        # 需要汇总的字段列表
        sum_fields = [
            'ammonia_sales_income', 'byproduct_income', 'equipment_residual_value', 'government_subsidy',
            'total_cash_inflow', 'hydrogen_plant_investment', 'nitrogen_raw_material_cost',
            'hydrogen_raw_material_cost', 'phase2_hydrogen_electricity_cost', 'maintenance_cost',
            'personnel_cost', 'other_operating_cost', 'water_cost', 'ammonia_electricity_cost',
            'catalyst_cost', 'financial_cost', 'total_cash_outflow', 'net_cash_flow_before_tax',
            'depreciation', 'vat_output', 'vat_input_operating', 'vat_input_fixed_assets',
            'vat_payable', 'vat_surcharge', 'total_vat_and_surcharge', 'income_tax', 'net_cash_flow_after_tax',
        ]

        # 计算各字段的合计
        for field in sum_fields:
            total_fields[field] = round_amount(sum(getattr(record, field, 0) or 0 for record in records))

        # 累计现金流量取最后一年的值
        last_record = records[-1] if records else None

        return CashFlowForAmmonia(
            project_id=project_id,
            year=-1,  # 合计行（使用-1表示合计）
            cumulative_net_cash_flow_before_tax=round_amount(last_record.cumulative_net_cash_flow_before_tax if last_record else 0),
            cumulative_net_cash_flow_after_tax=round_amount(last_record.cumulative_net_cash_flow_after_tax if last_record else 0),
            **total_fields
        )

