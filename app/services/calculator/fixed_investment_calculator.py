from typing import Dict, List
from config.fixed_params import default_unit_costs
from app.db.models.fixed_investment_item import FixedInvestmentItem
from app.db.models.operating_params import OperatingParams
from app.db.models.project import Project


class FixedInvestmentCalculator:
    """固定投资计算器，根据表格2的逻辑计算各项固定投资"""

    def __init__(self):
        self.default_unit_costs = default_unit_costs

    def get_scale_mapping(self, operating_params: OperatingParams) -> Dict[str, float]:
        """
        返回每个投资项名称对应的规模值映射
        
        Args:
            operating_params: 运营参数对象
            
        Returns:
            Dict[str, float]: 投资项名称到规模值的映射
        """
        return {
            "光伏电站": operating_params.pv_capacity_mw,
            "风电站": operating_params.wind_capacity_mw,
            "光伏送出线路": operating_params.pv_transmission_distance_km,
            "风电送出线路": operating_params.wind_transmission_distance_km,
            "升压站": operating_params.grid_step_up_station_capacity_mva,
            "降压站": operating_params.grid_step_down_station_capacity_mva,
            "电池储能系统": operating_params.electrochemical_energy_storage_scale_mw,  #来自容量优化配置结果，单位为MW
            "制氢工厂": operating_params.h2_plant_capacity_mw,
            "储氢系统": operating_params.h2_storage_capacity_tons,
            "制氢公辅设施": None,
            "其他1": None,
            "其他2": None,
            "其他3": None,
            "其他4": None,
            "其他5": None
        }

    def calculate_project_fixed_investment(self, operating_params: OperatingParams) -> list[FixedInvestmentItem]:
        """
        计算项目的固定投资
        
        Args:
            operating_params: 运营参数对象
            
        Returns:
            List[FixedInvestmentItem]: 固定投资项列表
        """
        if not operating_params:
            raise ValueError("缺少运营参数，无法计算固定投资")
        
        # 获取scale映射
        scale_mapping = self.get_scale_mapping(operating_params)
        
        # 计算各项固定投资
        investment_items: List[FixedInvestmentItem] = []
        
        # 遍历default_unit_costs，并创建FixedInvestmentItem对象
        for item_name, item_data in self.default_unit_costs.items():
            # 从映射中获取对应的scale值
            scale_value = scale_mapping.get(item_name, None)
            item: FixedInvestmentItem = FixedInvestmentItem(
                item_name=item_name,
                scale=scale_value,  # 设置从operating_params获取的scale值
                scale_unit=item_data["scale_unit"],
                unit_cost=item_data["unit_cost"],
                unit_cost_unit=item_data["unit"]
            )
            # 计算总投资
            if scale_value is None: #说明是固定投资
                item.total_investment = item_data["total_investment"]
            else:
                item.calculate_total_investment()
            investment_items.append(item)
        
        # 创建总计行
        total = FixedInvestmentItem(
            item_name="总计",
            is_total_row=True,
            total_investment=0.0
        )
        for item in investment_items:
            total.total_investment += (item.total_investment or 0.0)
        investment_items.append(total)
        
        return investment_items
