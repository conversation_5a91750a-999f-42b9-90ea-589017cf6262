from typing import List, Dict

from app.db.models import CashFlow, EnergyMaterialBalance, FixedInvestmentItem, OperatingParams
from app.utils.calculate_utils import round_amount
from config.fixed_params import clash_flow_params


class CashFlowCalculator:
    """现金流量计算器，根据现金流量表的逻辑计算1+2+25年的现金流量数据"""

    def __init__(self):
        self.operation_years = clash_flow_params.operation_years  # 运营年限
        self.construction_years = clash_flow_params.construction_years  # 建设年限

        # 固定参数
        self.residual_value_rate = clash_flow_params.residual_value_rate  # 设备残值率5%
        self.depreciation_rate = clash_flow_params.depreciation_rate  # 折旧率95%
        self.depreciation_years = clash_flow_params.depreciation_years  # 折旧年限24年
        self.vat_rate_13 = clash_flow_params.vat_rate_13  # 增值税率13%
        self.vat_rate_9 = clash_flow_params.vat_rate_9  # 固定资产增值税率9%
        self.vat_surcharge_rate = clash_flow_params.vat_surcharge_rate  # 增值税附加12%
        self.h2_raw_material_cost_rate = clash_flow_params.h2_raw_material_cost_rate  # 制氢原材料成本系数
        self.h2_water_consumption_rate = clash_flow_params.h2_water_consumption_rate  # 制氢水耗25吨/吨氢
        self.h2_wastewater_rate = clash_flow_params.h2_wastewater_rate  # 制氢废水15吨/吨氢
        self.water_price_rate = clash_flow_params.water_price_rate  # 水价0.0004万元/吨
        self.h2_maintenance_rate = clash_flow_params.h2_maintenance_rate  # 制氢站维修费用2%
        self.h2_insurance_rate = clash_flow_params.h2_insurance_rate  # 制氢站保险0.2%
        self.pv_wind_operating_rate = clash_flow_params.pv_wind_operating_rate  # 光伏风电运营管理成本0.2%
        self.pv_wind_insurance_rate = clash_flow_params.pv_wind_insurance_rate  # 光伏风电保险费用0.1%
        self.electricity_financial_cost_rate = clash_flow_params.electricity_financial_cost_rate  # 电力财务成本1%
        self.h2_personnel_cost = clash_flow_params.h2_personnel_cost  # 制氢站人员支出200万元/年
        self.h2_other_operating_cost = clash_flow_params.h2_other_operating_cost  # 制氢站其他运营管理支出200万元/年
        self.government_subsidy_params = clash_flow_params.government_subsidy_params
        # 税收优惠政策参数
        self.income_tax_free_years = clash_flow_params.income_tax_free_years  # 所得税免税年限
        self.income_tax_half_years = clash_flow_params.income_tax_half_years  # 所得税减半年限
        self.income_tax_rate = clash_flow_params.income_tax_rate  # 所得税率20%
        self.project_payback_period = 0.0  # 项目回收期

    def calculate_cash_flow(self, operating_params, energy_material_balance_records, fixed_investment_items) -> tuple[List[CashFlow],float]:
        """计算现金流量表"""
        cash_flow_records: List[CashFlow] = []
        if not operating_params:
            raise ValueError("缺少运营参数")
        if not energy_material_balance_records:
            raise ValueError("缺少能量物质平衡数据")
        if not fixed_investment_items:
            raise ValueError("缺少固定投资数据")

        # 获取基础数据
        energy_balance_records = {record.year: record for record in energy_material_balance_records}
        fixed_investments = {item.item_name: item for item in fixed_investment_items}

        # 计算建设期数据（年份-2, -1）
        for year in range(-self.construction_years, 0):
            record = self._calculate_period_record(
                None, year, operating_params, energy_balance_records, fixed_investments
            )
            cash_flow_records.append(record)

        # 计算运营期数据（年份1-25）
        for year in range(1, self.operation_years + 1):
            record = self._calculate_period_record(
                None, year, operating_params, energy_balance_records, fixed_investments,cash_flow_records[-1]
            )
            cash_flow_records.append(record)

        # 计算合计数据（年份0）
        total_record = self._calculate_total_record(None, cash_flow_records)
        cash_flow_records.insert(0, total_record)  # 插入到开头

        return cash_flow_records,self.project_payback_period

    def _calculate_period_record(
        self, project_id: int, year: int, operating_params:OperatingParams,
        energy_balance_records: Dict[int, EnergyMaterialBalance], fixed_investments: Dict[str, FixedInvestmentItem],
        last_record: CashFlow = None
    ) -> CashFlow:
        """计算任意年份的记录（建设期或运营期）"""
        # 获取当年的能量物质平衡数据
        energy_balance = energy_balance_records.get(year)
        if not energy_balance:
            raise ValueError(f"缺少第{year}年的能量物质平衡数据")

        # 创建基础记录
        record = CashFlow(project_id=project_id, year=year)

        # === 1. 计算现金流入 ===
        # 1.1-1.3 产品销售收入
        record.h2_transport_sales_income = round_amount(energy_balance.h2_sales_transport_tons * operating_params.h2_price_transport / 10)
        record.h2_chemical_sales_income = round_amount(energy_balance.h2_sales_chemical_tons * operating_params.h2_price_chemical / 10)
        record.o2_sales_income = round_amount(operating_params.o2_price_per_ton * energy_balance.o2_production_tons / 10000)

        # 1.6-1.7 电力销售收入
        record.pv_electricity_sales_income = round_amount(operating_params.pv_grid_price_tax_included_yuan_per_kwh *
                                                          energy_balance.pv_grid_sales_mwh * 1000 / 10000)
        record.wind_electricity_sales_income = round_amount(operating_params.grid_price_tax_included_yuan_per_kwh *
                                                            energy_balance.wind_grid_sales_mwh * 1000 / 10000)

        # 1.9-1.20 设备残值收入（仅第25年）
        if year == 25:
            record.pv_station_residual_value = round_amount(self._get_investment_amount(fixed_investments, "光伏电站") * self.residual_value_rate)
            record.wind_station_residual_value = round_amount(self._get_investment_amount(fixed_investments, "风电站") * self.residual_value_rate)
            record.pv_transmission_residual_value = round_amount(self._get_investment_amount(fixed_investments, "光伏送出线路") * self.residual_value_rate)
            record.wind_transmission_residual_value = round_amount(self._get_investment_amount(fixed_investments, "风电送出线路") * self.residual_value_rate)
            record.step_up_station_residual_value = round_amount(self._get_investment_amount(fixed_investments, "升压站") * self.residual_value_rate)
            record.step_down_station_residual_value = round_amount(self._get_investment_amount(fixed_investments, "降压站") * self.residual_value_rate)
            record.battery_storage_residual_value = round_amount(self._get_investment_amount(fixed_investments, "电池储能系统") * self.residual_value_rate)
            record.h2_plant_residual_value = round_amount(self._get_investment_amount(fixed_investments, "制氢工厂") * self.residual_value_rate)
            record.h2_storage_residual_value = round_amount(self._get_investment_amount(fixed_investments, "储氢系统") * self.residual_value_rate)
            record.h2_auxiliary_residual_value = round_amount(self._get_investment_amount(fixed_investments, "制氢公辅设施") * self.residual_value_rate)

        # 1.21 政府补贴
        record.government_subsidy = round_amount(self.government_subsidy_params * 1000 / 10000)
        # 计算现金流入合计（确保所有字段都有值）
        record.total_cash_inflow = round_amount(
            (record.h2_transport_sales_income or 0) + (record.h2_chemical_sales_income or 0) + (record.o2_sales_income or 0) +
            (record.ammonia_sales_income or 0) + (record.steam_sales_income or 0) +
            (record.pv_electricity_sales_income or 0) + (record.wind_electricity_sales_income or 0) + (record.other_income or 0) +
            (record.pv_station_residual_value or 0) + (record.wind_station_residual_value or 0) +
            (record.pv_transmission_residual_value or 0) + (record.wind_transmission_residual_value or 0) +
            (record.step_up_station_residual_value or 0) + (record.step_down_station_residual_value or 0) +
            (record.battery_storage_residual_value or 0) + (record.h2_plant_residual_value or 0) +
            (record.h2_storage_residual_value or 0) + (record.h2_auxiliary_residual_value or 0) +
            (record.synthesis_equipment_residual_value or 0) + (record.other_equipment_residual_value or 0) +
            (record.government_subsidy or 0)
        )

        # === 2. 计算现金流出 ===
        # 建设期第2年（-1年）进行固定投资 2.1~2.10
        if year == -1:
            record.pv_station_investment = round_amount(self._get_investment_amount(fixed_investments, "光伏电站"))
            record.wind_station_investment = round_amount(self._get_investment_amount(fixed_investments, "风电站"))
            record.pv_transmission_investment = round_amount(self._get_investment_amount(fixed_investments, "光伏送出线路"))
            record.wind_transmission_investment = round_amount(self._get_investment_amount(fixed_investments, "风电送出线路"))
            record.step_up_station_investment = round_amount(self._get_investment_amount(fixed_investments, "升压站"))
            record.step_down_station_investment = round_amount(self._get_investment_amount(fixed_investments, "降压站"))
            record.battery_storage_investment = round_amount(self._get_investment_amount(fixed_investments, "电池储能系统"))
            record.h2_plant_investment = round_amount(self._get_investment_amount(fixed_investments, "制氢工厂"))
            record.h2_storage_investment = round_amount(self._get_investment_amount(fixed_investments, "储氢系统"))
            record.h2_auxiliary_investment = round_amount(self._get_investment_amount(fixed_investments, "制氢公辅设施"))

        # 2.11 合成设备投资
        # 2.12 其他设备投资

        # 运营成本（有生产就有运营成本）2.13~2.19
        if energy_balance.h2_production_tons > 0:
            record.h2_raw_materials_cost = round_amount(energy_balance.h2_production_tons * self.h2_raw_material_cost_rate)
            record.h2_water_cost = round_amount(energy_balance.h2_production_tons * self.h2_water_consumption_rate * self.water_price_rate)
            record.h2_wastewater_cost = round_amount(energy_balance.h2_production_tons * self.h2_wastewater_rate * self.water_price_rate)
            record.h2_personnel_cost = round_amount(self.h2_personnel_cost)
            record.h2_other_operating_cost = round_amount(self.h2_other_operating_cost)

            # 制氢站维修费用和保险 2.14和2.19
            h2_related_investment = (
                self._get_investment_amount(fixed_investments, "制氢工厂") +
                self._get_investment_amount(fixed_investments, "储氢系统") +
                self._get_investment_amount(fixed_investments, "制氢公辅设施")
            )
            if h2_related_investment > 0:
                record.h2_maintenance_cost = round_amount(h2_related_investment * self.h2_maintenance_rate)
                record.h2_insurance_cost = round_amount(h2_related_investment * self.h2_insurance_rate)
            else:
                raise Exception("计算运营成本时投资金额之和<=0")
        # 光伏和风电运营成本（运营期才有）2.20~2.25
        if year > 0:
            pv_capacity = operating_params.pv_capacity_mw
            wind_capacity = operating_params.wind_capacity_mw
            total_capacity = pv_capacity + wind_capacity

            pv_related_investment = (
                self._get_investment_amount(fixed_investments, "光伏电站") +
                self._get_investment_amount(fixed_investments, "光伏送出线路")
            )
            wind_related_investment = (
                self._get_investment_amount(fixed_investments, "风电站") +
                self._get_investment_amount(fixed_investments, "风电送出线路")
            )
            shared_investment = (
                self._get_investment_amount(fixed_investments, "升压站") +
                self._get_investment_amount(fixed_investments, "降压站") +
                self._get_investment_amount(fixed_investments, "电池储能系统")
            )
            #2.20~2.22
            record.pv_operating_cost = round_amount(pv_related_investment * self.pv_wind_operating_rate + shared_investment * self.pv_wind_operating_rate * pv_capacity / total_capacity)
            record.pv_insurance_cost = round_amount(pv_related_investment * self.pv_wind_insurance_rate + shared_investment * self.pv_wind_insurance_rate * pv_capacity / total_capacity)
            record.pv_financial_cost = round_amount(record.pv_electricity_sales_income * self.electricity_financial_cost_rate)
            #2.23~2.25
            record.wind_operating_cost = round_amount(wind_related_investment * self.pv_wind_operating_rate + shared_investment * self.pv_wind_operating_rate * wind_capacity / total_capacity)
            record.wind_insurance_cost = round_amount(wind_related_investment * self.pv_wind_insurance_rate + shared_investment * self.pv_wind_insurance_rate * wind_capacity / total_capacity)
            record.wind_financial_cost = round_amount(record.wind_electricity_sales_income * self.electricity_financial_cost_rate)

        # 计算现金流出合计（确保所有字段都有值）
        record.total_cash_outflow = round_amount(
            (record.pv_station_investment or 0) + (record.wind_station_investment or 0) +
            (record.pv_transmission_investment or 0) + (record.wind_transmission_investment or 0) +
            (record.step_up_station_investment or 0) + (record.step_down_station_investment or 0) +
            (record.battery_storage_investment or 0) + (record.h2_plant_investment or 0) +
            (record.h2_storage_investment or 0) + (record.h2_auxiliary_investment or 0) +
            (record.synthesis_equipment_investment or 0) + (record.other_equipment_investment or 0) +
            (record.h2_raw_materials_cost or 0) + (record.h2_maintenance_cost or 0) +
            (record.h2_personnel_cost or 0) + (record.h2_other_operating_cost or 0) +
            (record.h2_water_cost or 0) + (record.h2_wastewater_cost or 0) + (record.h2_insurance_cost or 0) +
            (record.pv_operating_cost or 0) + (record.pv_insurance_cost or 0) + (record.pv_financial_cost or 0) +
            (record.wind_operating_cost or 0) + (record.wind_insurance_cost or 0) + (record.wind_financial_cost or 0)
        )

        # === 3-4. 净现金流量 ===
        record.net_cash_flow_before_tax = round_amount((record.total_cash_inflow or 0) - (record.total_cash_outflow or 0))
        if year <= -1:  # 建设期
            record.cumulative_net_cash_flow_before_tax = round_amount(record.net_cash_flow_before_tax)
        if year > 0:  # 运营期
            if last_record:
                record.cumulative_net_cash_flow_before_tax = round_amount(
                    last_record.cumulative_net_cash_flow_before_tax + record.net_cash_flow_before_tax
                )
            else:
                record.cumulative_net_cash_flow_before_tax = round_amount(record.net_cash_flow_before_tax)
        # === 5. 折旧 ===
        if year >0 and year < 25:
            investment_items = clash_flow_params.investment_items # 固定投资和投资项有一定差别，故需单独维护配置
            total_investment = sum([item.total_investment or 0 for item in fixed_investments.values() if item.item_name in investment_items])  # 计算所有固定资产投资项目的总投资金额 fixed_investments.values(): 获取字典中所有的值
            record.depreciation = round_amount(total_investment * self.depreciation_rate / self.depreciation_years)
        # 最后一列特殊处理
        if year >= 25:
            record.depreciation =0 if year> clash_flow_params.depreciation_years else clash_flow_params.depreciation_last_year_value/1.09/clash_flow_params.depreciation_years
        # === 6. 税务计算 ===
        if year > 0:
            if (record.total_cash_inflow or 0) > 0:
                record.vat_output = round_amount((record.total_cash_inflow or 0) / 1.13 * self.vat_rate_13)
            else:
                record.vat_output = 0.0

            if (record.total_cash_outflow or 0) > 0:
                record.vat_input_operating = round_amount((record.total_cash_outflow or 0) / 1.13 * self.vat_rate_13)
            else:
                record.vat_input_operating = 0.0

            if (record.depreciation or 0) > 0:
                record.vat_input_fixed_assets = round_amount((record.depreciation or 0) / 1.09 * self.vat_rate_9)
            else:
                record.vat_input_fixed_assets = 0.0

            record.vat_payable = round_amount(record.vat_output - record.vat_input_operating - record.vat_input_fixed_assets) # 6.4 缴纳增值税
            record.vat_surcharge = round_amount((record.vat_payable or 0) * self.vat_surcharge_rate)
            record.total_vat_and_surcharge = round_amount(record.vat_payable + record.vat_surcharge)

        # === 7. 所得税 ===
        if year>0:
            record.income_tax = round_amount(self._calculate_income_tax(year, record.net_cash_flow_before_tax, record.depreciation, record.total_vat_and_surcharge))

        # === 8-9. 税后现金流 ===
        if year>=-1:
            record.net_cash_flow_after_tax = round_amount((record.net_cash_flow_before_tax or 0) - (record.income_tax or 0) -
                                                          (record.total_vat_and_surcharge or 0))
            if year == -1:
                record.cumulative_net_cash_flow_after_tax = round_amount(record.net_cash_flow_after_tax)
            elif year > 0:
                # 今年累积现金流 = 今年现金流+上年累计现金流
                record.cumulative_net_cash_flow_after_tax = round_amount(record.net_cash_flow_after_tax + last_record.cumulative_net_cash_flow_after_tax)
                if record.cumulative_net_cash_flow_after_tax>0 and last_record.cumulative_net_cash_flow_after_tax<0:
                    self.project_payback_period = round(year - last_record.cumulative_net_cash_flow_after_tax/record.net_cash_flow_after_tax,2) # 计算项目回收期
        return record

    def _get_investment_amount(self, fixed_investments: Dict[str, FixedInvestmentItem], item_name: str) -> float:
        """获取投资金额"""
        item = fixed_investments.get(item_name)
        return item.total_investment if item else 0.0

    def _calculate_income_tax(self, year: int, net_cash_flow_before_tax: float, depreciation: float, total_vat_and_surcharge: float) -> float:
        """计算所得税（按三免三减半政策）"""
        if year <= 0:  # 建设期不征收所得税
            return 0.0
        elif year <= self.income_tax_free_years:
            return 0.0  # 前3年免税
        elif year <= self.income_tax_free_years + self.income_tax_half_years:
            # 4-6年减半征收
            # （以第四年为例）：=IF(AND(I51 > 0, (I51 - I53 - I59) > 0), (I51 - I53 - I59) * 0.2 * 0.5, 0)
            if net_cash_flow_before_tax > 0 and (net_cash_flow_before_tax - depreciation - total_vat_and_surcharge) > 0:
                return (net_cash_flow_before_tax - depreciation - total_vat_and_surcharge) * self.income_tax_rate * 0.5
            else:
                return 0.0
        else:
            # 7-25年全额征收
            if net_cash_flow_before_tax > 0 and (net_cash_flow_before_tax - depreciation - total_vat_and_surcharge) > 0:
                return (net_cash_flow_before_tax - depreciation - total_vat_and_surcharge) * self.income_tax_rate
            else:
                return 0.0

    def _calculate_total_record(self, project_id: int, records: List[CashFlow]) -> CashFlow:
        """计算合计记录（年份0）"""

        # 汇总所有年份的数据
        total_fields = {}

        # 需要汇总的字段列表
        sum_fields = [
            'h2_transport_sales_income', 'h2_chemical_sales_income', 'o2_sales_income',
            'ammonia_sales_income', 'steam_sales_income', 'pv_electricity_sales_income',
            'wind_electricity_sales_income', 'other_income', 'pv_station_residual_value',
            'wind_station_residual_value', 'pv_transmission_residual_value',
            'wind_transmission_residual_value', 'step_up_station_residual_value',
            'step_down_station_residual_value', 'battery_storage_residual_value',
            'h2_plant_residual_value', 'h2_storage_residual_value', 'h2_auxiliary_residual_value',
            'synthesis_equipment_residual_value', 'other_equipment_residual_value',
            'government_subsidy', 'total_cash_inflow',
            'pv_station_investment', 'wind_station_investment', 'pv_transmission_investment',
            'wind_transmission_investment', 'step_up_station_investment', 'step_down_station_investment',
            'battery_storage_investment', 'h2_plant_investment', 'h2_storage_investment',
            'h2_auxiliary_investment', 'synthesis_equipment_investment', 'other_equipment_investment',
            'h2_raw_materials_cost', 'h2_maintenance_cost', 'h2_personnel_cost',
            'h2_other_operating_cost', 'h2_water_cost', 'h2_wastewater_cost', 'h2_insurance_cost',
            'pv_operating_cost', 'pv_insurance_cost', 'pv_financial_cost',
            'wind_operating_cost', 'wind_insurance_cost', 'wind_financial_cost',
            'total_cash_outflow', 'net_cash_flow_before_tax', 'depreciation',
            'vat_output', 'vat_input_operating', 'vat_input_fixed_assets',
            'vat_payable', 'vat_surcharge', 'total_vat_and_surcharge',
            'income_tax', 'net_cash_flow_after_tax',
        ]

        # 计算各字段的合计
        for field in sum_fields:
            total_fields[field] = round_amount(sum(getattr(record, field, 0) or 0 for record in records))

        # 累计现金流量取最后一年的值
        last_record = records[-1] if records else None

        return CashFlow(
            project_id=project_id,
            year=0,  # 合计行
            cumulative_net_cash_flow_before_tax=round_amount(last_record.cumulative_net_cash_flow_before_tax if last_record else 0),
            cumulative_net_cash_flow_after_tax=round_amount(last_record.cumulative_net_cash_flow_after_tax if last_record else 0),
            **total_fields
        )

