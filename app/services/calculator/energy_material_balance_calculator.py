
from typing import List

from app.db.models import EnergyMaterialBalance, Project
from config.fixed_params import energy_material_balance_params


class EnergyMaterialBalanceCalculator:
    """能量物质平衡计算器，根据表格3的逻辑计算25年的能量物质平衡数据"""
    def __init__(self):
        self.operation_years = energy_material_balance_params.operation_years  # 运营年限
        self.construction_years = energy_material_balance_params.construction_years  # 建设年限

        # 固定参数
        self.grid_sales_ratio = energy_material_balance_params.grid_sales_ratio  # 上网比例
        self.loss_ratio = energy_material_balance_params.loss_ratio  # 弃电及网损比例
        self.transport_h2_ratio = energy_material_balance_params.transport_h2_ratio  # 交通氢能销量比例
        self.o2_h2_ratio = energy_material_balance_params.o2_h2_ratio  # 制氧量与制氢量比例
        self.wind_efficiency_decline_rate = energy_material_balance_params.wind_efficiency_decline_rate  # 风电效率年衰减率0.5%
        self.h2_consumption_increase_rate = energy_material_balance_params.h2_consumption_increase_rate  # 制氢能耗年增长率1%
        self.h2_equipment_replacement_year = energy_material_balance_params.h2_equipment_replacement_year  # 制氢设备更换年份

    def calculate_energy_material_balance(self, operating_params) -> List[EnergyMaterialBalance]:
        """计算能量物质平衡表"""
        energy_material_balance_records: List[EnergyMaterialBalance] = []

        if not operating_params:
            raise ValueError("缺少运营参数")

        # 计算建设期数据（年份-2, -1）
        for year in range(-self.construction_years, 0):
            record = self._create_construction_period_record(None, year)
            energy_material_balance_records.append(record)

        # 计算运营期数据（年份1-25）
        previous_pv_efficiency = 1.0  # 初始光伏效率
        previous_h2_consumption_factor = 1.0  # 初始制氢能耗系数

        for year in range(1, self.operation_years + 1):
            record = self._calculate_operation_period_record(
                None, year, operating_params,
                previous_pv_efficiency, previous_h2_consumption_factor
            )
            energy_material_balance_records.append(record)

            # 更新下一年的基础值
            previous_pv_efficiency = record.pv_efficiency
            previous_h2_consumption_factor = record.h2_consumption_factor
       #能量物质平衡表的合计没有被引用，且字段较少，可以在数据库查询时求和计算。
        return energy_material_balance_records

    def _create_construction_period_record(self, project_id: int, year: int) -> EnergyMaterialBalance:
        """创建建设期记录（无生产数据）"""
        return EnergyMaterialBalance(
            project_id=project_id,
            year=year,
            # 建设期所有生产相关数据为0或None
            pv_attenuation_coefficient=0.0,
            pv_efficiency=0.0,
            wind_efficiency=0.0,
            grid_sales_ratio=self.grid_sales_ratio,
            h2_consumption_increase=0.0,
            h2_consumption_factor=0.0,
            pv_generation_mwh=0.0,
            wind_generation_mwh=0.0,
            total_generation_mwh=0.0,
            grid_sales_mwh=0.0,
            pv_grid_sales_mwh=0.0,
            wind_grid_sales_mwh=0.0,
            loss_mwh=0.0,
            h2_electricity_mwh=0.0,
            h2_production_tons=0.0,
            h2_sales_transport_tons=0.0,
            h2_sales_chemical_tons=0.0,
            o2_production_tons=0.0
        )

    def _calculate_operation_period_record(
        self, project_id: int, year: int, operating_params,
        previous_pv_efficiency: float, previous_h2_consumption_factor: float
    ) -> EnergyMaterialBalance:
        """计算运营期记录"""

        # 1. 计算年度系数
        pv_attenuation_coefficient = self._calculate_pv_attenuation_coefficient(year)
        pv_efficiency = round(self._calculate_pv_efficiency(year, previous_pv_efficiency, pv_attenuation_coefficient),3)
        wind_efficiency = round(self._calculate_wind_efficiency(year),3)
        h2_consumption_increase = self._calculate_h2_consumption_increase(year)
        h2_consumption_factor = self._calculate_h2_consumption_factor(
            year, previous_h2_consumption_factor, h2_consumption_increase
        )

        # 2. 计算发电量 (MWh)
        pv_generation_mwh = self._calculate_pv_generation(
            operating_params.pv_capacity_mw,
            operating_params.pv_utilization_hours,
            pv_efficiency
        )
        pv_generation_mwh = round(pv_generation_mwh,0)
        wind_generation_mwh = self._calculate_wind_generation(
            operating_params.wind_capacity_mw,
            operating_params.wind_utilization_hours,
            wind_efficiency
        )
        total_generation_mwh = pv_generation_mwh + wind_generation_mwh

        # 3. 计算电量分配 (MWh)
        grid_sales_mwh = total_generation_mwh * self.grid_sales_ratio #保障性上网电量
        pv_grid_sales_mwh = round(pv_generation_mwh * self.grid_sales_ratio,0) #光伏保障性上网电量
        wind_grid_sales_mwh = wind_generation_mwh * self.grid_sales_ratio #风电保障性上网电量
        loss_mwh = total_generation_mwh * self.loss_ratio
        h2_electricity_mwh = total_generation_mwh - grid_sales_mwh - loss_mwh #年制电氢量

        # 4. 计算产品产量 (t)
        h2_production_tons = self._calculate_h2_production(
            h2_electricity_mwh,
            operating_params.h2_base_consumption_kwh_per_kg,
            h2_consumption_factor
        )
        h2_sales_transport_tons = round(h2_production_tons * self.transport_h2_ratio,3)
        h2_sales_chemical_tons = round(h2_production_tons - h2_sales_transport_tons,3)
        o2_production_tons = h2_production_tons * self.o2_h2_ratio

        return EnergyMaterialBalance(
            project_id=project_id,
            year=year,
            pv_attenuation_coefficient=pv_attenuation_coefficient,
            pv_efficiency=pv_efficiency,
            wind_efficiency=wind_efficiency,
            grid_sales_ratio=self.grid_sales_ratio,
            h2_consumption_increase=h2_consumption_increase,
            h2_consumption_factor=h2_consumption_factor,
            pv_generation_mwh=pv_generation_mwh,
            wind_generation_mwh=wind_generation_mwh,
            total_generation_mwh=total_generation_mwh,
            grid_sales_mwh=grid_sales_mwh,
            pv_grid_sales_mwh=pv_grid_sales_mwh,
            wind_grid_sales_mwh=wind_grid_sales_mwh,
            loss_mwh=loss_mwh,
            h2_electricity_mwh=h2_electricity_mwh,
            h2_production_tons=h2_production_tons,
            h2_sales_transport_tons=h2_sales_transport_tons,
            h2_sales_chemical_tons=h2_sales_chemical_tons,
            o2_production_tons=o2_production_tons
        )

    def _calculate_pv_attenuation_coefficient(self, year: int) -> float:
        """计算光伏衰减系数"""
        if year == 1:
            return 0.0  # 第1年无衰减
        elif year == 2:
            return 0.02  # 第2年衰减2%
        else:
            return 0.005  # 第3年及以后每年衰减0.5%

    def _calculate_pv_efficiency(self, year: int, previous_efficiency: float, attenuation_coefficient: float) -> float:
        """计算光伏效率"""
        if year == 1:
            return 1.0  # 第1年效率为100%
        else:
            return previous_efficiency - attenuation_coefficient

    def _calculate_wind_efficiency(self, year: int) -> float:
        """计算风电效率（每年减少0.5%）"""
        return 1.0 - (year - 1) * self.wind_efficiency_decline_rate

    def _calculate_h2_consumption_increase(self, year: int) -> float:
        """计算制氢能耗增加率"""
        if year == 1:
            return 0.0  # 第1年无增加
        else:
            return self.h2_consumption_increase_rate  # 每年增加1%

    def _calculate_h2_consumption_factor(
        self, year: int, previous_factor: float, consumption_increase: float
    ) -> float:
        """计算制氢能耗系数"""
        if year == 1:
            return 1.0  # 第1年为100%
        elif year == self.h2_equipment_replacement_year:
            return 1.0  # 第14年设备更换，重置为100%
        else:
            return previous_factor + consumption_increase

    def _calculate_pv_generation(self, capacity_mw: float, utilization_hours: float, efficiency: float) -> float:
        """计算光伏发电量 (MWh)"""
        return capacity_mw * utilization_hours * efficiency

    def _calculate_wind_generation(self, capacity_mw: float, utilization_hours: float, efficiency: float) -> float:
        """计算风电发电量 (MWh)"""
        return capacity_mw * utilization_hours * efficiency

    def _calculate_h2_production(
        self, h2_electricity_mwh: float, unit_consumption_kwh_per_kg: float, consumption_factor: float
    ) -> float:
        """计算制氢量 (t)"""
        # 转换单位：MWh -> kWh，kg -> t
        h2_electricity_kwh = h2_electricity_mwh * 1000  # MWh转kWh
        h2_production_kg = h2_electricity_kwh / unit_consumption_kwh_per_kg / consumption_factor
        return round(h2_production_kg / 1000,3)  # kg转t,保留三位小数
