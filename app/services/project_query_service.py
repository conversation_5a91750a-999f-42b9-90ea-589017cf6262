from typing import List, Optional, Dict, Any

from sqlalchemy.ext.asyncio import AsyncSession

from app.db.repositories.cash_flow_repository import CashFlowRepository
from app.db.repositories.energy_material_balance_repository import EnergyMaterialBalanceRepository
from app.db.repositories.fixed_investment_repository import FixedInvestmentRepository
from app.db.repositories.operating_params_repository import OperatingParamsRepository
from app.db.repositories.project_repository import ProjectRepository
from app.utils.model_serializer import serialize_model, serialize_model_list


class ProjectQueryService:
    """项目查询服务类，处理项目数据查询相关业务逻辑"""

    def __init__(self, db: AsyncSession):
        self.db = db
        self.project_repo = ProjectRepository(db)
        self.operating_params_repo = OperatingParamsRepository(db)
        self.fixed_investment_repo = FixedInvestmentRepository(db)
        self.energy_material_balance_repo = EnergyMaterialBalanceRepository(db)
        self.cash_flow_repo = CashFlowRepository(db)

    async def get_project_complete_data(self, project_id: int) -> Optional[Dict[str, Any]]:
        """
        获取项目的完整数据，包括所有关联表的数据
        
        Args:
            project_id: 项目ID
            
        Returns:
            包含项目完整数据的字典，如果项目不存在则返回None
        """
        # 获取项目基本信息
        project = await self.project_repo.get_with_all_relations(project_id)
        if not project:
            return None

        project_data = self._format_project_data(project)

        return project_data

    async def get_project_complete_data_by_name(self, project_name):
        project = await self.project_repo.get_by_name(project_name)
        if not project:
            return None
        project_data = self._format_project_data(project)
        return project_data

    async def get_projects_list(self, skip: int = 0, limit: int = 100) -> List[Dict[str, Any]]:
        """
        获取项目列表（基本信息）

        Args:
            skip: 跳过的记录数
            limit: 限制返回的记录数

        Returns:
            项目基本信息列表
        """
        projects = await self.project_repo.get_projects_with_basic_info(skip, limit)

        project_list = []
        for project in projects:
            project_info = {
                "id": project.id,
                "name": project.name,
                "description": project.description,
                "irr_before_tax": project.irr_before_tax,
                "npv_before_tax": project.npv_before_tax,
                "irr_after_tax": project.irr_after_tax,
                "npv_after_tax": project.npv_after_tax,
                "created_at": project.created_at,
                "updated_at": project.updated_at,
            }
            project_list.append(project_info)

        return project_list

    def _format_project_data(self, project):
        """格式化项目数据（同步方法，因为只是格式化已加载的数据）"""
        operating_params = project.operating_params
        fixed_investments = project.fixed_investments
        energy_material_balance_records = project.energy_material_balance_records
        cash_flow_records = project.cash_flow_records

        # 构建完整的项目数据
        project_data = {
            "project_info": {
                "id": project.id,
                "name": project.name,
                "irr_before_tax": round(project.irr_before_tax, 3),
                "npv_before_tax": round(project.npv_before_tax, 3),
                "irr_after_tax": round(project.irr_after_tax, 3),
                "npv_after_tax": round(project.npv_after_tax, 3),
                "description": project.description,
                "created_at": project.created_at,
                "updated_at": project.updated_at
            },
            "operating_params": serialize_model(operating_params),
            "fixed_investments": serialize_model_list(fixed_investments),
            "energy_material_balance": serialize_model_list(energy_material_balance_records),
            "cash_flow": serialize_model_list(cash_flow_records),
        }
        return project_data
