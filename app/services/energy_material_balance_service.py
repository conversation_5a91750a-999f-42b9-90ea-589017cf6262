from typing import List, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession

from app.db.repositories.energy_material_balance_repository import EnergyMaterialBalanceRepository
from app.db.models import EnergyMaterialBalance


class EnergyMaterialBalanceService:
    """能量物质平衡服务类，处理能量物质平衡相关业务逻辑"""

    def __init__(self, db: AsyncSession):
        self.db = db
        self.repository = EnergyMaterialBalanceRepository(db)

    async def get_by_project_id(self, project_id: int) -> List[Dict[str, Any]]:
        """
        获取指定项目的所有能量物质平衡记录

        Args:
            project_id: 项目ID

        Returns:
            能量物质平衡记录列表
        """
        records = await self.repository.get_by_project_id(project_id)
        return [self._format_energy_material_balance(record) for record in records]



    def _format_energy_material_balance(self, record: EnergyMaterialBalance) -> Dict[str, Any]:
        """格式化能量物质平衡记录数据"""
        return {
            "id": record.id,
            "project_id": record.project_id,
            "year": record.year,
            "period_description": record.period_description,
            "is_construction_period": record.is_construction_period,
            "is_operation_period": record.is_operation_period,
            
            # 年度系数
            "pv_attenuation_coefficient": record.pv_attenuation_coefficient,
            "pv_efficiency": record.pv_efficiency,
            "wind_efficiency": record.wind_efficiency,
            "grid_sales_ratio": record.grid_sales_ratio,
            "h2_consumption_increase": record.h2_consumption_increase,
            "h2_consumption_factor": record.h2_consumption_factor,
            
            # 发电量
            "pv_generation_mwh": record.pv_generation_mwh,
            "wind_generation_mwh": record.wind_generation_mwh,
            "total_generation_mwh": record.total_generation_mwh,
            
            # 电量分配
            "grid_sales_mwh": record.grid_sales_mwh,
            "pv_grid_sales_mwh": record.pv_grid_sales_mwh,
            "wind_grid_sales_mwh": record.wind_grid_sales_mwh,
            "loss_mwh": record.loss_mwh,
            "h2_electricity_mwh": record.h2_electricity_mwh,

            # 产成品
            "h2_production_tons": record.h2_production_tons,
            "h2_sales_transport_tons": record.h2_sales_transport_tons,
            "h2_sales_chemical_tons": record.h2_sales_chemical_tons,
            "o2_production_tons": record.o2_production_tons,
            
            "calculated_at": record.calculated_at,
            "created_at": record.created_at,
            "updated_at": record.updated_at
        }
