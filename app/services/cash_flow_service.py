from typing import List, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession

from app.db.repositories.cash_flow_repository import CashFlowRepository
from app.db.models import CashFlow
from app.utils.model_serializer import serialize_model_list


class CashFlowService:
    """现金流服务类，处理现金流相关业务逻辑"""

    def __init__(self, db: AsyncSession):
        self.db = db
        self.repository = CashFlowRepository(db)

    async def get_by_project_id(self, project_id: int) -> List[Dict[str, Any]]:
        """
        获取指定项目的所有现金流记录

        Args:
            project_id: 项目ID

        Returns:
            现金流记录列表
        """
        records = await self.repository.get_by_project_id(project_id)
        return serialize_model_list(records)


