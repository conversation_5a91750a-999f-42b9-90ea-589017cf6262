from typing import List, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession

from app.db.repositories.fixed_investment_repository import FixedInvestmentRepository
from app.db.models import FixedInvestmentItem


class FixedInvestmentService:
    """固定投资服务类，处理固定投资相关业务逻辑"""

    def __init__(self, db: AsyncSession):
        self.db = db
        self.repository = FixedInvestmentRepository(db)

    async def get_by_project_id(self, project_id: int) -> List[Dict[str, Any]]:
        """
        获取指定项目的所有固定投资项

        Args:
            project_id: 项目ID

        Returns:
            固定投资项列表
        """
        items = await self.repository.get_by_project_id(project_id)
        return [self._format_fixed_investment_item(item) for item in items]

    def _format_fixed_investment_item(self, item: FixedInvestmentItem) -> Dict[str, Any]:
        """格式化固定投资项数据"""
        return {
            "id": item.id,
            "project_id": item.project_id,
            "item_name": item.item_name,
            "scale": item.scale,
            "scale_unit": item.scale_unit,
            "unit_cost": item.unit_cost,
            "unit_cost_unit": item.unit_cost_unit,
            "offset": item.offset,
            "total_investment": item.total_investment,
            "is_total_row": item.is_total_row,
            "created_at": item.created_at,
            "updated_at": item.updated_at
        }
