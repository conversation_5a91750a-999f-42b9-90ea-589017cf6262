from typing import List, Optional
from sqlalchemy import select, delete
from sqlalchemy.ext.asyncio import AsyncSession

from app.db.models import FixedInvestmentItem
from app.db.repositories.base import BaseRepository


class FixedInvestmentRepository(BaseRepository[FixedInvestmentItem]):
    """固定投资仓库类"""
    
    def __init__(self, db: AsyncSession):
        super().__init__(FixedInvestmentItem, db)
    
    async def get_by_project_id(self, project_id: int) -> List[FixedInvestmentItem]:
        """通过项目ID获取所有固定投资项"""
        query = select(FixedInvestmentItem).filter(FixedInvestmentItem.project_id == project_id)
        result = await self.db.execute(query)
        return result.scalars().all()
    
    async def create_batch(self, fixed_investments: List[FixedInvestmentItem]) -> List[FixedInvestmentItem]:
        """批量创建固定投资项"""
        for item in fixed_investments:
            self.db.add(item)
        await self.db.commit()
        
        # 刷新所有对象以获取生成的ID
        for item in fixed_investments:
            await self.db.refresh(item)
        
        return fixed_investments
    
    async def create_batch_no_commit(self, fixed_investments: List[FixedInvestmentItem]) -> List[FixedInvestmentItem]:
        """批量创建固定投资项，不自动提交事务"""
        for item in fixed_investments:
            self.db.add(item)
        await self.db.flush()
        
        # 刷新所有对象以获取生成的ID
        for item in fixed_investments:
            await self.db.refresh(item)
        
        return fixed_investments
    
    async def delete_by_project_id(self, project_id: int) -> int:
        """删除项目的所有固定投资项，返回删除的数量"""
        query = delete(FixedInvestmentItem).where(FixedInvestmentItem.project_id == project_id)
        result = await self.db.execute(query)
        await self.db.commit()
        return result.rowcount
    
    async def get_total_investment_by_project(self, project_id: int) -> float:
        """获取项目的总投资额"""
        items = await self.get_by_project_id(project_id)
        return sum(item.total_investment or 0 for item in items)
    
    async def get_by_item_name(self, project_id: int, item_name: str) -> Optional[FixedInvestmentItem]:
        """通过项目ID和投资项名称获取固定投资项"""
        query = select(FixedInvestmentItem).filter(
            FixedInvestmentItem.project_id == project_id,
            FixedInvestmentItem.item_name == item_name
        )
        result = await self.db.execute(query)
        return result.scalars().first()
