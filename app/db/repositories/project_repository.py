from typing import List, Optional
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from app.db.models import Project
from app.db.repositories.base import BaseRepository


class ProjectRepository(BaseRepository[Project]):
    """项目仓库类"""
    
    def __init__(self, db: AsyncSession):
        super().__init__(Project, db)
    
    async def get_by_name(self, name: str) -> Optional[Project]:
        """通过项目名称获取项目及其所有关联数据"""
        query = select(Project).options(
            selectinload(Project.operating_params),
            selectinload(Project.fixed_investments),
            selectinload(Project.energy_material_balance_records),
            selectinload(Project.cash_flow_records)
        ).filter(Project.name == name)
        result = await self.db.execute(query)
        return result.scalars().first()
    
    async def get_with_all_relations(self, project_id: int) -> Optional[Project]:
        """获取项目及其所有关联数据"""
        query = select(Project).options(
            selectinload(Project.operating_params),
            selectinload(Project.fixed_investments),
            selectinload(Project.energy_material_balance_records),
            selectinload(Project.cash_flow_records)
        ).filter(Project.id == project_id)
        
        result = await self.db.execute(query)
        return result.scalars().first()
    
    async def get_projects_with_basic_info(self, skip: int = 0, limit: int = 100) -> List[Project]:
        """获取项目基本信息列表（不包含关联数据）"""
        query = select(Project).offset(skip).limit(limit).order_by(Project.created_at.desc())
        result = await self.db.execute(query)
        return result.scalars().all()
