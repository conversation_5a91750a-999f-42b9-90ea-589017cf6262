from typing import List, Optional
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.db.models import OperatingParams
from app.db.repositories.base import BaseRepository


class OperatingParamsRepository(BaseRepository[OperatingParams]):
    """运营参数仓库类"""
    
    def __init__(self, db: AsyncSession):
        super().__init__(OperatingParams, db)
    
    async def get_by_project_id(self, project_id: int) -> Optional[OperatingParams]:
        """通过项目ID获取运营参数"""
        query = select(OperatingParams).filter(OperatingParams.project_id == project_id)
        result = await self.db.execute(query)
        return result.scalars().first()
    
    async def create_from_project(self, operating_params: OperatingParams) -> OperatingParams:
        """创建运营参数记录"""
        self.db.add(operating_params)
        await self.db.commit()
        await self.db.refresh(operating_params)
        return operating_params
    
    async def create_from_project_no_commit(self, operating_params: OperatingParams) -> OperatingParams:
        """创建运营参数记录，不自动提交事务"""
        self.db.add(operating_params)
        await self.db.flush()
        await self.db.refresh(operating_params)
        return operating_params
    
    async def update_by_project_id(self, project_id: int, **kwargs) -> Optional[OperatingParams]:
        """通过项目ID更新运营参数"""
        existing = await self.get_by_project_id(project_id)
        if existing:
            for key, value in kwargs.items():
                if hasattr(existing, key):
                    setattr(existing, key, value)
            await self.db.commit()
            await self.db.refresh(existing)
            return existing
        return None
    
    async def delete_by_project_id(self, project_id: int) -> bool:
        """通过项目ID删除运营参数"""
        existing = await self.get_by_project_id(project_id)
        if existing:
            await self.db.delete(existing)
            await self.db.commit()
            return True
        return False
