from typing import List, Optional
from sqlalchemy import select, delete
from sqlalchemy.ext.asyncio import AsyncSession

from app.db.models import CashFlow
from app.db.repositories.base import BaseRepository


class CashFlowRepository(BaseRepository[CashFlow]):
    """现金流仓库类"""
    
    def __init__(self, db: AsyncSession):
        super().__init__(CashFlow, db)
    
    async def get_by_project_id(self, project_id: int) -> List[CashFlow]:
        """通过项目ID获取所有现金流记录"""
        query = select(CashFlow).filter(
            CashFlow.project_id == project_id
        ).order_by(CashFlow.year)
        result = await self.db.execute(query)
        return result.scalars().all()
    
    async def get_by_project_and_year(self, project_id: int, year: int) -> Optional[CashFlow]:
        """通过项目ID和年份获取现金流记录"""
        query = select(CashFlow).filter(
            CashFlow.project_id == project_id,
            CashFlow.year == year
        )
        result = await self.db.execute(query)
        return result.scalars().first()
    
    async def create_batch(self, records: List[CashFlow]) -> List[CashFlow]:
        """批量创建现金流记录"""
        for record in records:
            self.db.add(record)
        await self.db.commit()
        
        # 刷新所有对象以获取生成的ID
        for record in records:
            await self.db.refresh(record)
        
        return records
    
    async def create_batch_no_commit(self, records: List[CashFlow]) -> List[CashFlow]:
        """批量创建现金流记录，不自动提交事务"""
        for record in records:
            self.db.add(record)
        await self.db.flush()
        
        # 刷新所有对象以获取生成的ID
        for record in records:
            await self.db.refresh(record)
        
        return records
    
    async def delete_by_project_id(self, project_id: int) -> int:
        """删除项目的所有现金流记录，返回删除的数量"""
        query = delete(CashFlow).where(CashFlow.project_id == project_id)
        result = await self.db.execute(query)
        await self.db.commit()
        return result.rowcount
    
    async def get_construction_period_records(self, project_id: int) -> List[CashFlow]:
        """获取建设期现金流记录（year <= 0）"""
        query = select(CashFlow).filter(
            CashFlow.project_id == project_id,
            CashFlow.year <= 0
        ).order_by(CashFlow.year)
        result = await self.db.execute(query)
        return result.scalars().all()
    
    async def get_operation_period_records(self, project_id: int) -> List[CashFlow]:
        """获取运营期现金流记录（year > 0）"""
        query = select(CashFlow).filter(
            CashFlow.project_id == project_id,
            CashFlow.year > 0
        ).order_by(CashFlow.year)
        result = await self.db.execute(query)
        return result.scalars().all()
    
    async def get_net_cash_flows_before_tax(self, project_id: int) -> List[float]:
        """获取税前净现金流列表（用于IRR和NPV计算）"""
        records = await self.get_by_project_id(project_id)
        return [record.net_cash_flow_before_tax for record in records if record.year != 0]
    
    async def get_net_cash_flows_after_tax(self, project_id: int) -> List[float]:
        """获取税后净现金流列表（用于IRR和NPV计算）"""
        records = await self.get_by_project_id(project_id)
        return [record.net_cash_flow_after_tax for record in records if record.year != 0]
