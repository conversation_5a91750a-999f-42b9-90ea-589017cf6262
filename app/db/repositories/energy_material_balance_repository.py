from typing import List, Optional
from sqlalchemy import select, delete
from sqlalchemy.ext.asyncio import AsyncSession

from app.db.models import EnergyMaterialBalance
from app.db.repositories.base import BaseRepository


class EnergyMaterialBalanceRepository(BaseRepository[EnergyMaterialBalance]):
    """能量物质平衡仓库类"""
    
    def __init__(self, db: AsyncSession):
        super().__init__(EnergyMaterialBalance, db)
    
    async def get_by_project_id(self, project_id: int) -> List[EnergyMaterialBalance]:
        """通过项目ID获取所有能量物质平衡记录"""
        query = select(EnergyMaterialBalance).filter(
            EnergyMaterialBalance.project_id == project_id
        ).order_by(EnergyMaterialBalance.year)
        result = await self.db.execute(query)
        return result.scalars().all()
    
    async def get_by_project_and_year(self, project_id: int, year: int) -> Optional[EnergyMaterialBalance]:
        """通过项目ID和年份获取能量物质平衡记录"""
        query = select(EnergyMaterialBalance).filter(
            EnergyMaterialBalance.project_id == project_id,
            EnergyMaterialBalance.year == year
        )
        result = await self.db.execute(query)
        return result.scalars().first()
    
    async def create_batch(self, records: List[EnergyMaterialBalance]) -> List[EnergyMaterialBalance]:
        """批量创建能量物质平衡记录"""
        for record in records:
            self.db.add(record)
        await self.db.commit()
        
        # 刷新所有对象以获取生成的ID
        for record in records:
            await self.db.refresh(record)
        
        return records
    
    async def create_batch_no_commit(self, records: List[EnergyMaterialBalance]) -> List[EnergyMaterialBalance]:
        """批量创建能量物质平衡记录，不自动提交事务"""
        for record in records:
            self.db.add(record)
        await self.db.flush()
        
        # 刷新所有对象以获取生成的ID
        for record in records:
            await self.db.refresh(record)
        
        return records
    
    async def delete_by_project_id(self, project_id: int) -> int:
        """删除项目的所有能量物质平衡记录，返回删除的数量"""
        query = delete(EnergyMaterialBalance).where(EnergyMaterialBalance.project_id == project_id)
        result = await self.db.execute(query)
        await self.db.commit()
        return result.rowcount
    
    async def get_construction_period_records(self, project_id: int) -> List[EnergyMaterialBalance]:
        """获取建设期记录（year < 0）"""
        query = select(EnergyMaterialBalance).filter(
            EnergyMaterialBalance.project_id == project_id,
            EnergyMaterialBalance.year < 0
        ).order_by(EnergyMaterialBalance.year)
        result = await self.db.execute(query)
        return result.scalars().all()
    
    async def get_operation_period_records(self, project_id: int) -> List[EnergyMaterialBalance]:
        """获取运营期记录（year > 0）"""
        query = select(EnergyMaterialBalance).filter(
            EnergyMaterialBalance.project_id == project_id,
            EnergyMaterialBalance.year > 0
        ).order_by(EnergyMaterialBalance.year)
        result = await self.db.execute(query)
        return result.scalars().all()
