import os
import logging
from typing import AsyncGenerator
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import declarative_base, sessionmaker

from config import settings

# 获取数据库URL
db_url = settings.DATABASE_URL

# 提取SQLite数据库文件路径（如果使用的是SQLite）
db_file_path = None
if db_url.startswith("sqlite:///"):
    # 从SQLite URL中提取文件路径
    db_file_path = db_url.replace("sqlite:///", "")

# 创建SQLite URL兼容性处理
if db_url.startswith("sqlite"):
    # SQLite URL needs to be adjusted for asyncio
    db_url = db_url.replace("sqlite", "sqlite+aiosqlite", 1)

# 创建异步引擎 // 连接某种数据库
# 为SQLite启用外键约束
if db_url.startswith("sqlite"):
    # SQLite需要特殊配置来启用外键约束
    engine = create_async_engine(
        db_url,
        echo=settings.DATABASE_ECHO,
        future=True,
        connect_args={
            "check_same_thread": False,
        },
        # 为每个连接启用外键约束
        pool_pre_ping=True,
    )

    # 添加事件监听器来启用外键约束
    from sqlalchemy import event

    @event.listens_for(engine.sync_engine, "connect")
    def set_sqlite_pragma(dbapi_connection, connection_record):
        """为SQLite连接启用外键约束"""
        cursor = dbapi_connection.cursor()
        cursor.execute("PRAGMA foreign_keys=ON")
        cursor.close()
else:
    # 其他数据库的标准配置
    engine = create_async_engine(
        db_url,
        echo=settings.DATABASE_ECHO,
        future=True,
    )

# 创建会话工厂//其实就是数据库连接池，绑定之前的引擎。会话是操作数据库的直接手段，每次访问数据库都应创建一个会话
SessionLocal = sessionmaker(
    autocommit=False,
    autoflush=False,
    bind=engine,
    expire_on_commit=False,
    class_=AsyncSession
)

# 创建Base类
Base = declarative_base()


async def init_db() -> None:
    """初始化数据库，创建所有表并输出创建的表名"""
    # 获取所有表名
    table_names = list(Base.metadata.tables.keys())
    logging.info(f"准备创建以下数据表: {', '.join(table_names) if table_names else '无'}")
    
    async with engine.begin() as conn:
        # 总是创建所有表，create_all不会覆盖已有的表
        await conn.run_sync(Base.metadata.create_all)
        logging.info("数据库表初始化完成")


async def close_db() -> None:
    """关闭数据库连接"""
    await engine.dispose()


async def get_db() -> AsyncGenerator[AsyncSession, None]:
    """获取数据库会话的依赖函数"""
    db = SessionLocal()
    try:
        yield db
    finally:
        await db.close()