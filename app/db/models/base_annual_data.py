import sqlalchemy
from sqlalchemy import Column, Foreign<PERSON>ey, Integer, DateTime
from sqlalchemy.orm import relationship
from datetime import datetime

from app.db.session import Base
from app.db.models.base import BaseModel


class BaseAnnualData(Base, BaseModel):
    """
    年度数据基类。
    为能量物质平衡表和未来的现金流量表提供通用的年度数据结构。
    采用"n+25年"的数据模式（建设期n年+运营期25年）。
    """
    __abstract__ = True  # 抽象基类，不会创建对应的数据库表

    # 通用年度标识字段
    project_id = Column(Integer, ForeignKey('projects.id'), nullable=False)
    year = Column(Integer, nullable=False, comment="年份标识：负数表示建设期（-2,-1），正数表示运营期（1,2,3...25）")
    
    # 数据计算时间戳
    calculated_at = Column(DateTime, default=datetime.utcnow, comment="数据计算时间")
    
    # 通用关系映射 - 子类需要重写此关系以使用正确的back_populates
    # project = relationship("Project")  # 注释掉，由子类定义具体关系
    

    @property
    def is_construction_period(self) -> bool:
        """判断是否为建设期"""
        return self.year < 0
    
    @property
    def is_operation_period(self) -> bool:
        """判断是否为运营期"""
        return self.year > 0
    
    @property
    def period_description(self) -> str:
        """获取期间描述"""
        if self.year < 0:
            return f"建设期第{self.year}年"
        elif self.year > 0:
            return f"运营期第{self.year}年"
        else:
            return "合计"
    
    # 为子类预留的扩展方法
    def validate_data(self) -> tuple[bool, str]:
        """
        数据验证方法，子类可以重写此方法实现特定的验证逻辑
        
        Returns:
            tuple: (是否有效, 错误消息)
        """
        if self.year == 0:
            return False, "年份不能为0"
        if self.year < -2 or self.year > 25:
            return False, "年份必须在建设期(-2,-1)或运营期(1-25)范围内"
        return True, "验证通过"
    
    def get_calculation_context(self) -> dict:
        """
        获取计算上下文信息，子类可以重写此方法提供特定的上下文
        
        Returns:
            dict: 计算上下文信息
        """
        return {
            "year": self.year,
            "period_type": "construction" if self.is_construction_period else "operation",
            "period_description": self.period_description,
            "calculated_at": self.calculated_at
        }
0