from dataclasses import dataclass

import sqlalchemy
from sqlalchemy import Column, ForeignKey, String, Text, Float, Integer, Boolean, Numeric
from sqlalchemy.orm import relationship

from app.db.session import Base
from app.db.models.base import BaseModel
from app.db.models.base_annual_data import BaseAnnualData

@dataclass
class CashFlow(BaseAnnualData):
    """
    现金流量表。
    存储每年计算得出的现金流量数据，完全对应现金流量表中的计算结果行。
    采用"1+2+25年"的数据模式（合计1年+建设期2年+运营期25年）。
    """
    __tablename__ = 'cash_flow'

    # === 1. 现金流入（万元） ===
    # 1.1-1.5 产品销售收入
    h2_transport_sales_income: float = Column(Float, default=0.0, comment="销售氢气（交通）收入 (1.1)")
    h2_chemical_sales_income: float = Column(Float, default=0.0, comment="销售氢气（化工）收入 (1.2)")
    o2_sales_income: float = Column(Float, default=0.0, comment="销售氧气（化工）收入 (1.3)")
    ammonia_sales_income: float = Column(Float, default=0.0, comment="销售液氨（化工）收入 (1.4)")
    steam_sales_income: float = Column(Float, default=0.0, comment="销售蒸汽（化工）收入 (1.5)")
    
    # 1.6-1.7 电力销售收入
    pv_electricity_sales_income: float = Column(Float, default=0.0, comment="光伏售电收入 (1.6)")
    wind_electricity_sales_income: float = Column(Float, default=0.0, comment="风电售电收入 (1.7)")
    
    # 1.8 其他收入
    other_income: float = Column(Float, default=0.0, comment="其他收入 (1.8)")
    
    # 1.9-1.20 设备残值收入（第25年）
    pv_station_residual_value: float = Column(Float, default=0.0, comment="光伏电站残值（5%）(1.9)")
    wind_station_residual_value: float = Column(Float, default=0.0, comment="风电站残值（5%）(1.10)")
    pv_transmission_residual_value: float = Column(Float, default=0.0, comment="光伏送出线路残值（5%）(1.11)")
    wind_transmission_residual_value: float = Column(Float, default=0.0, comment="风电送出线路残值（5%）(1.12)")
    step_up_station_residual_value: float = Column(Float, default=0.0, comment="升压站残值（5%）(1.13)")
    step_down_station_residual_value: float = Column(Float, default=0.0, comment="降压站残值（5%）(1.14)")
    battery_storage_residual_value: float = Column(Float, default=0.0, comment="电池储能系统残值（5%）(1.15)")
    h2_plant_residual_value: float = Column(Float, default=0.0, comment="制氢工厂残值（5%）(1.16)")
    h2_storage_residual_value: float = Column(Float, default=0.0, comment="储氢系统残值（5%）(1.17)")
    h2_auxiliary_residual_value: float = Column(Float, default=0.0, comment="制氢公辅设施残值（5%）(1.18)")
    synthesis_equipment_residual_value: float = Column(Float, default=0.0, comment="合成设备残值（5%）(1.19)")
    other_equipment_residual_value: float = Column(Float, default=0.0, comment="其他设备残值（5%）(1.20)")
    
    # 1.21 政府补贴
    government_subsidy: float = Column(Float, default=0.0, comment="政府直接补贴 (1.21)")
    
    # 现金流入合计
    total_cash_inflow: float = Column(Float, default=0.0, comment="现金流入合计")

    # === 2. 现金流出（万元） ===
    # 2.1-2.12 固定投资支出（建设期）
    pv_station_investment: float = Column(Float, default=0.0, comment="光伏电站投资 (2.1)")
    wind_station_investment: float = Column(Float, default=0.0, comment="风电站投资 (2.2)")
    pv_transmission_investment: float = Column(Float, default=0.0, comment="光伏送出线路投资 (2.3)")
    wind_transmission_investment: float = Column(Float, default=0.0, comment="风电送出线路投资 (2.4)")
    step_up_station_investment: float = Column(Float, default=0.0, comment="升压站投资 (2.5)")
    step_down_station_investment: float = Column(Float, default=0.0, comment="降压站投资 (2.6)")
    battery_storage_investment: float = Column(Float, default=0.0, comment="电池储能系统投资 (2.7)")
    h2_plant_investment: float = Column(Float, default=0.0, comment="制氢工厂投资 (2.8)")
    h2_storage_investment: float = Column(Float, default=0.0, comment="储氢系统投资 (2.9)")
    h2_auxiliary_investment: float = Column(Float, default=0.0, comment="制氢公辅设施投资 (2.10)")
    synthesis_equipment_investment: float = Column(Float, default=0.0, comment="合成设备投资 (2.11)")
    other_equipment_investment: float = Column(Float, default=0.0, comment="其他设备投资 (2.12)")
    
    # 2.13-2.25 运营成本支出
    h2_raw_materials_cost: float = Column(Float, default=0.0, comment="制氢站外购原材料（化学品）(2.13)")
    h2_maintenance_cost: float = Column(Float, default=0.0, comment="制氢站维修费用（2%）(2.14)")
    h2_personnel_cost: float = Column(Float, default=0.0, comment="制氢站人员支出 (2.15)")
    h2_other_operating_cost: float = Column(Float, default=0.0, comment="制氢站其他运营管理支出 (2.16)")
    h2_water_cost: float = Column(Float, default=0.0, comment="制氢水费 (2.17)")
    h2_wastewater_cost: float = Column(Float, default=0.0, comment="制氢排污水费 (2.18)")
    h2_insurance_cost: float = Column(Float, default=0.0, comment="制氢站保险 (2.19)")
    pv_operating_cost: float = Column(Float, default=0.0, comment="光伏运营、管理成本 (2.20)")
    pv_insurance_cost: float = Column(Float, default=0.0, comment="光伏保险费用 (2.21)")
    pv_financial_cost: float = Column(Float, default=0.0, comment="光伏财务成本 (2.22)")
    wind_operating_cost: float = Column(Float, default=0.0, comment="风电运营、管理成本 (2.23)")
    wind_insurance_cost: float = Column(Float, default=0.0, comment="风电保险费用 (2.24)")
    wind_financial_cost: float = Column(Float, default=0.0, comment="风电财务成本 (2.25)")
    
    # 现金流出合计
    total_cash_outflow: float = Column(Float, default=0.0, comment="现金流出合计")

    # === 3-4. 净现金流量 ===
    net_cash_flow_before_tax: float = Column(Float, default=0.0, comment="所得税前净现金流量")
    cumulative_net_cash_flow_before_tax: float = Column(Float, default=0.0, comment="所得税前累计净现金流量")

    # === 5. 折旧 ===
    depreciation: float = Column(Float, default=0.0, comment="折旧")

    # === 6. 税务计算 ===
    vat_output: float = Column(Float, default=0.0, comment="增值税销项（售氢售氧补贴按13%）(6.1)")
    vat_input_operating: float = Column(Float, default=0.0, comment="增值税进项（一）（运行成本按13%）(6.2)")
    vat_input_fixed_assets: float = Column(Float, default=0.0, comment="增值税进项（二）（固定资产按9%）(6.3)")
    vat_payable: float = Column(Float, default=0.0, comment="缴纳增值税 (6.4)")
    vat_surcharge: float = Column(Float, default=0.0, comment="缴纳增值税附加 (6.5)")
    total_vat_and_surcharge: float = Column(Float, default=0.0, comment="增值税及附加总额 (6.6)")

    # === 7-9. 所得税和税后现金流 ===
    income_tax: float = Column(Float, default=0.0, comment="所得税")
    net_cash_flow_after_tax: float = Column(Float, default=0.0, comment="税后净现金流")
    cumulative_net_cash_flow_after_tax: float = Column(Float, default=0.0, comment="税后累计现金流")

    # 重写关系映射以使用正确的back_populates
    project = relationship("Project", back_populates="cash_flow_records")

    __table_args__ = (
        sqlalchemy.UniqueConstraint('project_id', 'year', name='_cash_flow_project_year_uc'),
    )

    def __repr__(self):
        return (f"<CashFlow(id={self.id}, project_id={self.project_id}, "
                f"year={self.year}, net_cash_flow_after_tax={self.net_cash_flow_after_tax})>")

    def validate_data(self) -> tuple[bool, str]:
        """
        重写基类的数据验证方法，添加现金流量表特定的验证逻辑
        """
        # 先调用基类验证
        base_valid, base_msg = super().validate_data()
        if not base_valid:
            return base_valid, base_msg

        # 现金流量表特定验证
        if self.year == 0:  # 合计行
            # 合计行应该有汇总数据
            if not self.total_cash_inflow and not self.total_cash_outflow:
                return False, "合计行应该有汇总数据"

        if self.is_construction_period:
            # 建设期主要是投资支出，但也可能有收入（根据现金流量表.md）
            pass  # 不再限制建设期收入

        if self.is_operation_period:
            # 运营期应该有基本的收入数据
            if not any([self.h2_transport_sales_income, self.h2_chemical_sales_income,
                       self.pv_electricity_sales_income, self.wind_electricity_sales_income]):
                return False, "运营期应该有收入数据"

        return True, "验证通过"
