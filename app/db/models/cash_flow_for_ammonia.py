from dataclasses import dataclass

import sqlalchemy
from sqlalchemy import Column, Float
from sqlalchemy.orm import relationship
import numpy_financial as npf
from app.db.models.base_annual_data import BaseAnnualData


@dataclass
class CashFlowForAmmonia(BaseAnnualData):
    """
    液氨生产项目现金流量表。
    存储每年计算得出的现金流量数据，完全对应现金流量表中的计算结果行。
    采用"1+0+25年"的数据模式（合计1年+建设期第0年+运营期25年）。
    """
    __tablename__ = 'cash_flow_for_ammonia'

    # === 1. 现金流入（万元） ===
    # 1.1 售液氨收入
    ammonia_sales_income: float = Column(Float, default=0.0, comment="售液氨收入 (1.1)")
    
    # 1.2 副产品收入
    byproduct_income: float = Column(Float, default=0.0, comment="副产品收入 (1.2)")
    
    # 1.3 设备残值（5%）
    equipment_residual_value: float = Column(Float, default=0.0, comment="设备残值（5%）(1.3)")
    
    # 1.4 政府直接补贴
    government_subsidy: float = Column(Float, default=0.0, comment="政府直接补贴 (1.4)")

    # 现金流入合计
    total_cash_inflow: float = Column(Float, default=0.0, comment="现金流入合计")

    # === 2. 现金流出（万元） ===
    # 2.1 制氢建设投资
    hydrogen_plant_investment: float = Column(Float, default=0.0, comment="制氢建设投资 (2.1)")
    
    # 2.21 外购氮气原料
    nitrogen_raw_material_cost: float = Column(Float, default=0.0, comment="外购氮气原料 (2.21)")
    
    # 2.22 外购1期氢气原料
    hydrogen_raw_material_cost: float = Column(Float, default=0.0, comment="外购1期氢气原料 (2.22)")
    
    # 2.23 二期制氢用电成本
    phase2_hydrogen_electricity_cost: float = Column(Float, default=0.0, comment="二期制氢用电成本 (2.23)")
    
    # 2.3 维修费用（4%）
    maintenance_cost: float = Column(Float, default=0.0, comment="维修费用（4%）(2.3)")
    
    # 2.4 人员支出
    personnel_cost: float = Column(Float, default=0.0, comment="人员支出 (2.4)")
    
    # 2.5 其他运营管理支出
    other_operating_cost: float = Column(Float, default=0.0, comment="其他运营管理支出 (2.5)")
    
    # 2.6 水费
    water_cost: float = Column(Float, default=0.0, comment="水费 (2.6)")
    
    # 2.7 制液氨电费
    ammonia_electricity_cost: float = Column(Float, default=0.0, comment="制液氨电费 (2.7)")
    
    # 2.8 催化剂
    catalyst_cost: float = Column(Float, default=0.0, comment="催化剂 (2.8)")
    
    # 2.9 财务成本
    financial_cost: float = Column(Float, default=0.0, comment="财务成本 (2.9)")

    # 现金流出合计
    total_cash_outflow: float = Column(Float, default=0.0, comment="现金流出合计")

    # === 3. 所得税前净现金流量（万元） ===
    net_cash_flow_before_tax: float = Column(Float, default=0.0, comment="所得税前净现金流量")

    # === 4. 所得税前累计净现金流量（万元） ===
    cumulative_net_cash_flow_before_tax: float = Column(Float, default=0.0, comment="所得税前累计净现金流量")

    # === 5. 折旧（万元） ===
    depreciation: float = Column(Float, default=0.0, comment="折旧")

    # === 6. 税务计算 ===
    # 6.1 增值税销项（售氨按13%）
    vat_output: float = Column(Float, default=0.0, comment="增值税销项（售氨按13%）(6.1)")
    
    # 6.2 增值税进项（一）（运行成本按13%）
    vat_input_operating: float = Column(Float, default=0.0, comment="增值税进项（一）（运行成本按13%）(6.2)")
    
    # 6.3 增值税进项（二）（固定资产按9%）
    vat_input_fixed_assets: float = Column(Float, default=0.0, comment="增值税进项（二）（固定资产按9%）(6.3)")
    
    # 6.4 缴纳增值税
    vat_payable: float = Column(Float, default=0.0, comment="缴纳增值税 (6.4)")
    
    # 6.5 缴纳增值税附加
    vat_surcharge: float = Column(Float, default=0.0, comment="缴纳增值税附加 (6.5)")
    
    # 6.6 增值税及附加总额
    total_vat_and_surcharge: float = Column(Float, default=0.0, comment="增值税及附加总额 (6.6)")

    # === 7. 所得税（三免三减半） ===
    income_tax: float = Column(Float, default=0.0, comment="所得税（三免三减半）")

    # === 8. 税后净现金流（万元） ===
    net_cash_flow_after_tax: float = Column(Float, default=0.0, comment="税后净现金流")

    # === 9. 税后累计现金流（万元） ===
    cumulative_net_cash_flow_after_tax: float = Column(Float, default=0.0, comment="税后累计现金流")

    # 重写关系映射以使用正确的back_populates
    project = relationship("Project", back_populates="cash_flow_records_for_ammonia")

    __table_args__ = (
        sqlalchemy.UniqueConstraint('project_id', 'year', name='_cash_flow_project_year_uc'),
    )



    def validate_data(self) -> tuple[bool, str]:
        """
        重写基类的数据验证方法，添加液氨项目现金流量表特定的验证逻辑
        """
        # 先调用基类验证
        base_valid, base_msg = super().validate_data()
        if not base_valid:
            return base_valid, base_msg

        # 液氨项目现金流量表特定验证
        if self.year == 0:  # 合计行
            # 合计行应该有汇总数据
            if not self.total_cash_inflow and not self.total_cash_outflow:
                return False, "合计行应该有汇总数据"

        # 建设期第0年验证
        if self.is_construction_period and self.year == 0:
            # 建设期主要是投资支出
            if self.hydrogen_plant_investment <= 0:
                return False, "建设期第0年应该有制氢建设投资"

        if self.is_operation_period:
            # 运营期应该有基本的收入数据
            if not any([self.ammonia_sales_income, self.byproduct_income]):
                return False, "运营期应该有液氨销售收入或副产品收入"
            
            # 运营期应该有基本的成本数据
            if not any([self.nitrogen_raw_material_cost, self.hydrogen_raw_material_cost, 
                       self.phase2_hydrogen_electricity_cost, self.maintenance_cost]):
                return False, "运营期应该有运营成本数据"

        return True, "验证通过"
