from dataclasses import dataclass

import sqlalchemy
from sqlalchemy import Column, Float
from sqlalchemy.orm import relationship

from app.db.models.base_annual_data import BaseAnnualData


@dataclass
class EnergyMaterialBalance(BaseAnnualData):
    """
    能量物质平衡表。
    存储每年计算得出的所有运营数据，完全对应能量物质平衡表中的计算结果行。
    采用"2+25年"的数据模式（建设期2年+运营期25年）。
    """
    __tablename__ = 'energy_material_balance'

    # 1. 年度系数 (中间变量)
    pv_attenuation_coefficient = Column(Float, comment="光伏衰减系数 (1.1)")
    pv_efficiency = Column(Float, comment="当年光伏效率 (1.2)")
    wind_efficiency = Column(Float, comment="当年风电效率 (1.3)")
    grid_sales_ratio:float = Column(Float, comment="当年上网比例 (1.3)",default=0.2)
    h2_consumption_increase:float = Column(Float, comment="制氢能耗年增长率 (2.1)",default=0.01)
    h2_consumption_factor = Column(Float, comment="当年制氢能耗系数 (2.1)")

    # 2. 发电量 (MWh)
    pv_generation_mwh = Column(Float, comment="光伏发电量 (3.1)")
    wind_generation_mwh = Column(Float, comment="风电发电电量 (3.2)")
    total_generation_mwh = Column(Float, comment="风光总发电量 (3)")

    # 3. 电量分配 (MWh)
    grid_sales_mwh = Column(Float, comment="保障性上网电量 (4.1)")
    pv_grid_sales_mwh = Column(Float, comment="光伏保障性上网电量 (4.1.1)")
    wind_grid_sales_mwh = Column(Float, comment="风电保障性上网电量 (4.1.2)")
    loss_mwh = Column(Float, comment="弃电及网损电量 (4.2)")
    h2_electricity_mwh = Column(Float, comment="年制氢用电量 (4.3)")

    # 4. 产成品 (t)
    h2_production_tons = Column(Float, comment="年制氢量 (5)")
    h2_sales_transport_tons = Column(Float, comment="交通氢能销量 (5.1)")
    h2_sales_chemical_tons = Column(Float, comment="化工氢能销量 (5.2)")
    o2_production_tons = Column(Float, comment="年制氧量 (6)")

    # 重写关系映射以使用正确的back_populates
    project = relationship("Project", back_populates="energy_material_balance_records")

    __table_args__ = (
        sqlalchemy.UniqueConstraint('project_id', 'year', name='_energy_material_balance_project_year_uc'),
    )

    def __repr__(self):
        return (f"<EnergyMaterialBalance(id={self.id}, project_id={self.project_id}, "
                f"year={self.year}, h2_production_tons={self.h2_production_tons})>")

    def validate_data(self) -> tuple[bool, str]:
        """
        重写基类的数据验证方法，添加能量物质平衡表特定的验证逻辑
        """
        # 先调用基类验证
        base_valid, base_msg = super().validate_data()
        if not base_valid:
            return base_valid, base_msg

        # 能量物质平衡表特定验证
        if self.is_construction_period:
            # 建设期不应该有生产数据
            if any([self.pv_generation_mwh, self.wind_generation_mwh,
                   self.h2_production_tons, self.o2_production_tons]):
                return False, "建设期不应该有生产数据"

        if self.is_operation_period:
            # 运营期应该有基本的发电数据
            if not (self.pv_generation_mwh or self.wind_generation_mwh):
                return False, "运营期必须有发电数据"

        return True, "验证通过"
