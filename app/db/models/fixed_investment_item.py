from dataclasses import dataclass, field
from typing import List, Optional

from sqlalchemy import Column, Integer, String, Float, Boolean, ForeignKey
from sqlalchemy.orm import relationship

from app.db.models.base import BaseModel
from app.db.session import Base

@dataclass
class FixedInvestmentItem(Base,BaseModel):
    """表2 固定投资项模型 (Active Record 模式)
    代表表中的一行，并包含计算和持久化逻辑"""

    # --- 持久化字段 ---
    id = Column(Integer, primary_key=True)
    project_id = Column(Integer, ForeignKey('projects.id'), nullable=False)
    item_name = Column(String(100), nullable=False,comment='投资项名称')
    scale = Column(Float, nullable=True,comment='规模或容量，例如MW、km、t')
    scale_unit = Column(String(50), nullable=True,comment='规模单位，例如“MW”、“km”、“t”')
    unit_cost = Column(Float, nullable=True,comment='单价造价，例如400、550')
    unit_cost_unit = Column(String(50), nullable=True,comment='单价造价单位，例如“万元/MW”、“万元/km”、“万元/t”')
    offset:float = Column(Float, default=0.0)
    total_investment:float = Column(Float, nullable=True, default=0.0)
    is_total_row:bool = Column(Boolean, default=False, nullable=False)

    project = relationship("Project", back_populates="fixed_investments")

    def calculate_total_investment(self):
        # 计算总投资 单位不能是空字符串或none
        # 确保参与计算的字段不为None如果为None则手动设置总投资而不是计算
        if self.unit_cost is None or self.unit_cost == "" or self.scale is None or self.scale == 0:
           # 无法计算，保持当前值或设为0
           if self.total_investment is None:
               self.total_investment = 0.0
           return self.total_investment

        scale = self.scale
        unit_cost = self.unit_cost
        offset = self.offset or 0.0
        self.total_investment = (scale * unit_cost) + offset
        return self.total_investment