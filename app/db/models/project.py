import numpy_financial as npf
from sqlalchemy import Column, String, Float, Integer
from sqlalchemy.orm import relationship

# 删除直接导入，改为在relationship中使用字符串引用
# from app.db.models import CashFlow, FixedInvestmentItem, EnergyMaterialBalance
from app.db.models.base import BaseModel
from app.db.session import Base


class Project(Base, BaseModel):
    """
    核心项目实体。
    作为固定投资、运营参数和年度生产数据的中心枢纽。
    """
    __tablename__ = 'projects'

    id = Column(Integer, primary_key=True)
    name = Column(String(255), nullable=False, unique=True, comment='项目唯一名称')
    description = Column(String(1024), nullable=True, comment='项目描述')
    project_payback_period: float = Column(Float, default=0.0, comment="项目回收期（年）")
    # 根据现金流量表计算IRR
    irr_before_tax: float = Column(Float, default=0.0, comment="税前内部收益率")
    npv_before_tax: float = Column(Float, default=0.0, comment="税前财务净现值")
    irr_after_tax: float = Column(Float, default=0.0, comment="税后内部收益率")
    npv_after_tax: float = Column(Float, default=0.0, comment="税后财务净现值")

    # Relationships
    # 一个项目对应一套运营参数
    operating_params = relationship("OperatingParams", back_populates="project", uselist=False,
                                    cascade="all, delete-orphan")
    # 一个项目包含多个能量物质平衡记录
    energy_material_balance_records = relationship("EnergyMaterialBalance", back_populates="project",
                                                   cascade="all, delete-orphan")
    # 一个项目包含多个固定投资项
    fixed_investments = relationship("FixedInvestmentItem", back_populates="project", cascade="all, delete-orphan")
    # 一个项目包含多个现金流量记录
    cash_flow_records = relationship("CashFlow", back_populates="project", cascade="all, delete-orphan")
    # 液氨项目相关关系映射
    # 一个项目包含多个液氨现金流量记录
    cash_flow_records_for_ammonia = relationship("CashFlowForAmmonia", back_populates="project",
                                                 cascade="all, delete-orphan")
    # 一个项目包含多个能量流记录
    energy_flow_records = relationship("EnergyFlowForAmmonia", back_populates="project", cascade="all, delete-orphan")
    # 一个项目包含多个制氢成本分解记录
    hydrogen_cost_records = relationship("HydrogenCostBreakdown", back_populates="project",
                                         cascade="all, delete-orphan")

    def calculate_irr(self):
        """计算内部收益率"""
        cash_flows_before_tax = [record.net_cash_flow_before_tax for record in self.cash_flow_records if record.year != 0]
        cash_flows_after_tax = [record.net_cash_flow_after_tax for record in self.cash_flow_records if record.year != 0]
        self.irr_before_tax = npf.irr(cash_flows_before_tax)
        self.irr_after_tax = npf.irr(cash_flows_after_tax)
    def calculate_npv(self, discount_rate: float):
        """计算财务净现值"""
        cash_flows_before_tax = [record.net_cash_flow_before_tax for record in self.cash_flow_records if record.year>0]
        cash_flows_after_tax = [record.net_cash_flow_after_tax for record in self.cash_flow_records if record.year>0]
        # 查找year=-1的记录
        initial_investment_record = next((record for record in self.cash_flow_records if record.year == -1), None)
        initial_investment = initial_investment_record.net_cash_flow_before_tax if initial_investment_record else 0
        self.npv_before_tax = npf.npv(discount_rate, cash_flows_before_tax) + initial_investment
        self.npv_after_tax = npf.npv(discount_rate, cash_flows_after_tax) + initial_investment #初始投资税前税后是一样的，可见现金流量表E51和E61

    def __repr__(self):
        return f"<Project(id={self.id}, name={self.name})>"