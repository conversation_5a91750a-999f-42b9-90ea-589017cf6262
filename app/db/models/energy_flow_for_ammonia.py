from dataclasses import dataclass

import sqlalchemy
from sqlalchemy import Column, Float, Integer
from sqlalchemy.orm import relationship

from app.db.models.base_annual_data import BaseAnnualData


@dataclass
class EnergyFlowForAmmonia(BaseAnnualData):
    """
    液氨生产项目能量流表（氢+氧+电）。
    存储每年的合成氨产量、制氢衰减系数、单位制氢电耗和年电耗量等能量相关数据。
    采用"1+0+25年"的数据模式（合计1年+建设期第0年+运营期25年）。
    """
    __tablename__ = 'energy_flow_for_ammonia'

    # 合成氨产品（吨）
    ammonia_production: float = Column(Float, default=0.0, comment="合成氨产品（吨）")
    
    # 制氢衰减系数（%）
    hydrogen_degradation_coefficient: float = Column(Float, default=100.0, comment="制氢衰减系数（%）")
    
    # 年单位制氢电耗（kWh/标方）
    annual_unit_hydrogen_power_consumption: float = Column(Float, default=0.0, comment="年单位制氢电耗（kWh/标方）")
    
    # 年电耗量（万kWh）
    annual_power_consumption: float = Column(Float, default=0.0, comment="年电耗量（万kWh）")

    # 关系映射
    project = relationship("Project", back_populates="energy_flow_records")

    __table_args__ = (
        sqlalchemy.UniqueConstraint('project_id', 'year', name='_energy_flow_project_year_uc'),
    )

    def __repr__(self):
        return (f"<EnergyFlowForAmmonia(id={self.id}, project_id={self.project_id}, "
                f"year={self.year}, ammonia_production={self.ammonia_production})>")

    def validate_data(self) -> tuple[bool, str]:
        """
        重写基类的数据验证方法，添加能量流表特定的验证逻辑
        """
        # 先调用基类验证
        base_valid, base_msg = super().validate_data()
        if not base_valid:
            return base_valid, base_msg

        # 能量流表特定验证
        if self.year == 0:  # 合计行
            # 合计行应该有汇总的产量数据
            if self.ammonia_production <= 0:
                return False, "合计行应该有合成氨总产量数据"

        if self.is_operation_period:
            # 运营期应该有基本的能量数据
            if self.ammonia_production <= 0:
                return False, "运营期应该有合成氨产量数据"
            
            if self.hydrogen_degradation_coefficient <= 0 or self.hydrogen_degradation_coefficient > 100:
                return False, "制氢衰减系数应该在0-100%之间"
            
            if self.annual_unit_hydrogen_power_consumption <= 0:
                return False, "年单位制氢电耗应该大于0"
            
            if self.annual_power_consumption <= 0:
                return False, "年电耗量应该大于0"

        return True, "验证通过"
