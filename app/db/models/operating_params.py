from dataclasses import dataclass

import sqlalchemy
from sqlalchemy import Column, ForeignKey, String, Text, Float, Integer, Boolean, Numeric
from sqlalchemy.orm import relationship
from app.db.session import Base
from app.db.models.base import BaseModel

@dataclass
class OperatingParams(Base, BaseModel):
    """
    运营参数表。
    存储与项目运营相关的所有配置参数、规则和固定系数。
    """
    __tablename__ = 'operating_params'

    id = Column(Integer, primary_key=True)
    project_id = Column(Integer, ForeignKey('projects.id'), nullable=False, unique=True)



    # 光伏参数
    pv_capacity_mw = Column(Float, nullable=False, comment='光伏装机容量 (MW)')
    pv_utilization_hours = Column(Float, nullable=False, comment='光伏利用小时数 (h)')
    pv_degradation_y1_percent:float = Column(Float, default=2, comment='光伏首年衰减率 ')
    pv_degradation_ongoing_percent:float = Column(Float, default=0.5, comment='光伏后续年衰减率 ')
    pv_system_efficiency_percent:float = Column(Float, default=82.3, comment='光伏系统效率 (%)')
    pv_transmission_distance_km:float = Column(Float, default=50, comment='光伏输电距离 (km)')
    pv_equipment_depreciation_years:int = Column(Integer, default=25, comment='光伏设备折旧年限 (年)')
    pv_grid_price_tax_included_yuan_per_kwh:float = Column(Float, default=0.2829, comment='光伏含税上网电价 (元/kWh)')

    # 风电参数
    wind_capacity_mw = Column(Float, nullable=False, comment='风电装机容量 (MW)')
    wind_utilization_hours = Column(Float, nullable=False, comment='风电利用小时数 (h)')
    wind_system_efficiency_percent:float = Column(Float, default=82.3, comment='风电系统效率 (%)')
    wind_transmission_distance_km:float = Column(Float, default=50, comment='风电输电距离 (km)')
    wind_equipment_depreciation_years:float = Column(Integer, default=25, comment='风电设备折旧年限 (年)')
    grid_price_tax_included_yuan_per_kwh:float = Column(Float, default=0.2829, comment='风电含税上网电价 (元/kWh)')

    # 电网系统
    grid_transmission_length_km = Column(Float, nullable=False, comment='电网输电线路长度（km）')
    grid_transmission_unit_cost_wan_yuan_per_km = Column(Float, nullable=False, comment='输电线路单位造价（万元/km）')
    grid_step_up_station_capacity_mva = Column(Float, nullable=False, comment='升压站容量（MVA）')
    grid_step_up_station_unit_cost_wan_yuan_per_mva = Column(Float, nullable=False, comment='升压站单位造价（万元/MVA）')
    grid_step_down_station_capacity_mva = Column(Float, nullable=False, comment='降压站容量（MVA）')
    grid_step_down_station_unit_cost_wan_yuan_per_mva = Column(Float, nullable=False,comment='降压站单位造价（万元/MVA）')

    # 制氢厂参数
    h2_plant_capacity_mw: float = Column(Float, nullable=False, comment='制氢容量 (MW)')
    h2_consumption_kwh_per_nm3: float = Column(Float, default=4.9, comment='单位耗电量 (kWh/Nm³)')
    h2_base_consumption_kwh_per_kg: float = Column(Float, default=54.88, comment='单位耗电量 (kWh/kg)')
    h2_equipment_service_years: int = Column(Integer, default=25, comment='制氢设备使用年限 (年)')
    h2_consumption_increase_annual: float = Column(Float, default=0.01, comment='制氢能耗年增加率')
    h2_water_price_yuan_per_ton: float = Column(Float, default=8.6, comment='水价 (元/吨)')
    h2_wastewater_price_yuan_per_ton: float = Column(Float, default=40, comment='废水处理费 (元/吨)')
    h2_staff_count: int = Column(Integer, default=48, comment='员工人数')
    h2_staff_salary_wan_yuan_per_year: float = Column(Float, default=10, comment='员工年薪 (万元/年)')
    h2_price_transport: float = Column(Float, default=20, comment='交通用氢价格 (元/kg)')
    h2_price_chemical: float = Column(Float, default=16.8, comment='化工用氢价格 (元/kg)')
    h2_storage_investment_wan_yuan: float = Column(Float, default=0, comment='储氢投资 (万元)')
    h2_storage_capacity_tons: float = Column(Float, default=700, comment='储氢容量 (吨)')
    h2_equipment_depreciation_years: int = Column(Integer, default=25, comment='制氢设备折旧年限 (年)')
    o2_price_per_ton: float = Column(Float, default=200, comment='氧气价格 (元/吨)')

    # 融资参数
    loan_term_years: int = Column(Integer, default=25, comment='贷款年限 (年)')
    loan_interest_rate: float = Column(Float, default=0.05, comment='贷款利率')
    finance_land_rent_wan_yuan_per_year: float = Column(Float, default=10, comment='土地租金 (万元/年)')
    loan_ratio: float = Column(Float, default=0.8, comment='贷款比例')
    finance_loan_total_wan_yuan: float = Column(Float, default=0, comment='贷款总额 (万元)')

    # 液氨参数
    ammonia_price_yuan_per_ton: float = Column(Float, default=3500, comment='氨价格 (元/吨)')
    ammonia_consumption_tons_per_hour: float = Column(Float, default=20, comment='氨消耗量 (吨/小时)')

    #储能电站
    electrochemical_energy_storage_scale_mw: float = Column(Float, default=25, comment='电化学储能规模 (MW)')
    # Relationship
    project = relationship("Project", back_populates="operating_params")

