import sqlalchemy
from sqlalchemy import Column, Float, String
from sqlalchemy.orm import relationship

from app.db.models.base import BaseModel
from app.db.session import Base


class HydrogenCostBreakdown(Base, BaseModel):
    """
    制氢部分总成本表。
    记录制氢部分的详细成本构成，包括原材料费、水耗、电耗和其他运营费用。
    这是一个相对静态的配置表，主要用于成本计算的基础数据。
    """
    __tablename__ = 'hydrogen_cost_breakdown'

    # 关联项目ID
    project_id: int = Column(sqlalchemy.Integer, sqlalchemy.ForeignKey('projects.id'), nullable=False)

    # === 1. 外购原材料费 ===
    # 氢氧化钾
    koh_consumption: float = Column(Float, default=0.0, comment="氢氧化钾消耗量（吨/年）")
    koh_price: float = Column(Float, default=0.0, comment="氢氧化钾价格（万元/吨）")
    koh_annual_cost: float = Column(Float, default=0.0, comment="氢氧化钾年总成本（万元/年）")
    
    # 脱氧催化剂
    deoxy_catalyst_consumption: float = Column(Float, default=0.0, comment="脱氧催化剂消耗量（吨/三年）")
    deoxy_catalyst_price: float = Column(Float, default=0.0, comment="脱氧催化剂价格（万元/吨）")
    deoxy_catalyst_annual_cost: float = Column(Float, default=0.0, comment="脱氧催化剂年总成本（万元/年）")
    
    # 干燥剂
    desiccant_consumption: float = Column(Float, default=0.0, comment="干燥剂消耗量（吨/三年）")
    desiccant_price: float = Column(Float, default=0.0, comment="干燥剂价格（万元/吨）")
    desiccant_annual_cost: float = Column(Float, default=0.0, comment="干燥剂年总成本（万元/年）")
    
    # 支撑剂
    support_agent_consumption: float = Column(Float, default=0.0, comment="支撑剂消耗量（吨/三年）")
    support_agent_price: float = Column(Float, default=0.0, comment="支撑剂价格（万元/吨）")
    support_agent_annual_cost: float = Column(Float, default=0.0, comment="支撑剂年总成本（万元/年）")
    
    # 外购原材料费小计
    raw_materials_total_cost: float = Column(Float, default=0.0, comment="外购原材料费总计（万元/年）")

    # === 2. 水耗 ===
    # 脱盐水新鲜水
    desalinated_water_consumption: float = Column(Float, default=0.0, comment="脱盐水新鲜水消耗量（吨/年）")
    desalinated_water_price: float = Column(Float, default=0.0, comment="脱盐水新鲜水价格（元/吨）")
    desalinated_water_annual_cost: float = Column(Float, default=0.0, comment="脱盐水新鲜水年总成本（万元/年）")
    
    # 循环新鲜水
    circulating_water_consumption: float = Column(Float, default=0.0, comment="循环新鲜水消耗量（吨/年）")
    circulating_water_price: float = Column(Float, default=0.0, comment="循环新鲜水价格（元/吨）")
    circulating_water_annual_cost: float = Column(Float, default=0.0, comment="循环新鲜水年总成本（万元/年）")
    
    # 水耗小计
    water_total_consumption: float = Column(Float, default=0.0, comment="水耗总消耗量（吨/年）")
    water_total_cost: float = Column(Float, default=0.0, comment="水耗总成本（万元/年）")

    # === 3. 电耗 ===
    # 电解槽直流电耗
    electrolyzer_power_consumption: float = Column(Float, default=0.0, comment="电解槽直流电耗（kWh/标方）")
    electrolyzer_power_price: float = Column(Float, default=0.0, comment="电解槽直流电价格（元/kWh）")
    electrolyzer_annual_cost: float = Column(Float, default=0.0, comment="电解槽直流电年总成本（万元/年）")
    
    # 制氢系统BOP
    hydrogen_bop_power_consumption: float = Column(Float, default=0.0, comment="制氢系统BOP电耗（kWh/标方）")
    hydrogen_bop_power_price: float = Column(Float, default=0.0, comment="制氢系统BOP电价格（元/kWh）")
    hydrogen_bop_annual_cost: float = Column(Float, default=0.0, comment="制氢系统BOP年总成本（万元/年）")
    
    # 制氢整流损耗
    rectifier_loss_power_consumption: float = Column(Float, default=0.0, comment="制氢整流损耗电耗（kWh/标方）")
    rectifier_loss_power_price: float = Column(Float, default=0.0, comment="制氢整流损耗电价格（元/kWh）")
    rectifier_loss_annual_cost: float = Column(Float, default=0.0, comment="制氢整流损耗年总成本（万元/年）")
    
    # 压缩系统
    compression_power_consumption: float = Column(Float, default=0.0, comment="压缩系统电耗（kWh/标方）")
    compression_power_price: float = Column(Float, default=0.0, comment="压缩系统电价格（元/kWh）")
    compression_annual_cost: float = Column(Float, default=0.0, comment="压缩系统年总成本（万元/年）")
    
    # 公辅系统
    auxiliary_power_consumption: float = Column(Float, default=0.0, comment="公辅系统电耗（kWh/标方）")
    auxiliary_power_price: float = Column(Float, default=0.0, comment="公辅系统电价格（元/kWh）")
    auxiliary_annual_cost: float = Column(Float, default=0.0, comment="公辅系统年总成本（万元/年）")
    
    # 其他电耗
    other_power_consumption: float = Column(Float, default=0.0, comment="其他电耗（kWh/标方）")
    other_power_price: float = Column(Float, default=0.0, comment="其他电价格（元/kWh）")
    other_power_annual_cost: float = Column(Float, default=0.0, comment="其他电耗年总成本（万元/年）")
    
    # 电耗小计
    power_total_consumption: float = Column(Float, default=0.0, comment="总电耗（kWh/标方）")
    power_total_cost: float = Column(Float, default=0.0, comment="电耗总成本（万元/年）")

    # === 4. 其他运营费用 ===
    # 人员工资
    personnel_count: float = Column(Float, default=0.0, comment="人员数量（人）")
    personnel_salary_per_person: float = Column(Float, default=0.0, comment="人员工资（万元/人）")
    personnel_total_cost: float = Column(Float, default=0.0, comment="人员工资总成本（万元/年）")
    
    # 修理费
    maintenance_rate: float = Column(Float, default=0.04, comment="修理费率（固定资产的百分比）")
    maintenance_annual_cost: float = Column(Float, default=0.0, comment="修理费年总成本（万元/年）")
    
    # 其他运营管理支出
    other_operating_cost: float = Column(Float, default=0.0, comment="其他运营管理支出（万元/年）")
    
    # 财务费用
    financial_cost: float = Column(Float, default=0.0, comment="财务费用（万元）")
    
    # 其他运营费用小计
    other_operating_total_cost: float = Column(Float, default=0.0, comment="其他运营费用总计（万元/年）")

    # === 总成本 ===
    total_hydrogen_cost: float = Column(Float, default=0.0, comment="制氢部分总成本（万元/年）")

    # 关系映射
    project = relationship("Project", back_populates="hydrogen_cost_records")

    def __repr__(self):
        return (f"<HydrogenCostBreakdown(id={self.id}, project_id={self.project_id}, "
                f"total_cost={self.total_hydrogen_cost})>")

    def calculate_totals(self) -> None:
        """
        计算各项成本小计和总计
        """
        # 计算外购原材料费小计
        self.raw_materials_total_cost = (
            self.koh_annual_cost + 
            self.deoxy_catalyst_annual_cost + 
            self.desiccant_annual_cost + 
            self.support_agent_annual_cost
        )
        
        # 计算水耗小计
        self.water_total_cost = (
            self.desalinated_water_annual_cost + 
            self.circulating_water_annual_cost
        )
        
        # 计算电耗小计
        self.power_total_cost = (
            self.electrolyzer_annual_cost + 
            self.hydrogen_bop_annual_cost + 
            self.rectifier_loss_annual_cost + 
            self.compression_annual_cost + 
            self.auxiliary_annual_cost + 
            self.other_power_annual_cost
        )
        
        # 计算其他运营费用小计
        self.other_operating_total_cost = (
            self.personnel_total_cost + 
            self.maintenance_annual_cost + 
            self.other_operating_cost + 
            self.financial_cost
        )
        
        # 计算总成本
        self.total_hydrogen_cost = (
            self.raw_materials_total_cost + 
            self.water_total_cost + 
            self.power_total_cost + 
            self.other_operating_total_cost
        )

    def validate_data(self) -> tuple[bool, str]:
        """
        数据验证方法
        """
        # 基本验证
        if self.project_id <= 0:
            return False, "项目ID必须大于0"
        
        # 消耗量和价格应该大于等于0
        if any([
            self.koh_consumption < 0, self.koh_price < 0,
            self.deoxy_catalyst_consumption < 0, self.deoxy_catalyst_price < 0,
            self.desiccant_consumption < 0, self.desiccant_price < 0,
            self.support_agent_consumption < 0, self.support_agent_price < 0
        ]):
            return False, "原材料消耗量和价格不能为负数"
        
        if any([
            self.desalinated_water_consumption < 0, self.desalinated_water_price < 0,
            self.circulating_water_consumption < 0, self.circulating_water_price < 0
        ]):
            return False, "水耗消耗量和价格不能为负数"
        
        if self.personnel_count < 0 or self.personnel_salary_per_person < 0:
            return False, "人员数量和工资不能为负数"
        
        if self.maintenance_rate < 0 or self.maintenance_rate > 1:
            return False, "修理费率应该在0-100%之间"

        return True, "验证通过"
