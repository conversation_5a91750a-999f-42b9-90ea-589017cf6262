"""
模型序列化工具，避免重复的格式化代码
"""
from typing import Dict, Any, List
import datetime
from decimal import Decimal


def serialize_model(model) -> Dict[str, Any]:
    """
    通用的模型序列化函数
    
    Args:
        model: SQLAlchemy模型实例
        
    Returns:
        序列化后的字典
    """
    if model is None:
        return None
        
    result = {}
    
    # 获取所有列属性
    for column in model.__table__.columns:
        value = getattr(model, column.name)
        
        # 处理特殊类型
        if isinstance(value, datetime.datetime):
            result[column.name] = value
        elif isinstance(value, Decimal):
            result[column.name] = float(value)
        elif value is None and column.type.python_type == float:
            # 浮点型字段的None值转换为0.0
            result[column.name] = 0.0
        else:
            result[column.name] = value
            
    return result


def serialize_model_list(models: List) -> List[Dict[str, Any]]:
    """
    批量序列化模型列表
    
    Args:
        models: 模型实例列表
        
    Returns:
        序列化后的字典列表
    """
    return [serialize_model(model) for model in models]


def serialize_model_with_relations(model, exclude_relations: List[str] = None) -> Dict[str, Any]:
    """
    序列化模型及其关联对象
    
    Args:
        model: SQLAlchemy模型实例
        exclude_relations: 要排除的关联属性名列表
        
    Returns:
        序列化后的字典
    """
    if model is None:
        return None
        
    exclude_relations = exclude_relations or []
    result = serialize_model(model)
    
    # 处理关联对象
    for relationship_name in model.__mapper__.relationships.keys():
        if relationship_name in exclude_relations:
            continue
            
        related_obj = getattr(model, relationship_name)
        if related_obj is None:
            result[relationship_name] = None
        elif isinstance(related_obj, list):
            result[relationship_name] = serialize_model_list(related_obj)
        else:
            result[relationship_name] = serialize_model(related_obj)
            
    return result
