import functools
import logging
from typing import Callable, Any, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.exc import SQLAlchemyError

logger = logging.getLogger(__name__)


def transactional(rollback_on_exception: bool = True, 
                 commit_on_success: bool = True,
                 propagation: str = "REQUIRED"):
    """
    事务装饰器，类似于Java的@Transactional注解
    
    Args:
        rollback_on_exception: 发生异常时是否回滚事务，默认True
        commit_on_success: 成功时是否提交事务，默认True
        propagation: 事务传播行为，目前只支持REQUIRED
    
    Usage:
        @transactional()
        async def some_method(self, ...):
            # 业务逻辑
            pass
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs) -> Any:
            # 查找AsyncSession实例
            db_session = None
            
            # 从self中查找数据库会话
            if args and hasattr(args[0], 'db') and isinstance(args[0].db, AsyncSession):
                db_session = args[0].db
            
            # 从参数中查找AsyncSession
            if not db_session:
                for arg in args + tuple(kwargs.values()):
                    if isinstance(arg, AsyncSession):
                        db_session = arg
                        break
            
            if not db_session:
                raise ValueError("未找到AsyncSession实例，无法启动事务")
            
            # 检查是否已经在事务中
            if db_session.in_transaction():
                logger.debug("已在事务中，直接执行方法")
                return await func(*args, **kwargs)
            
            # 开始新事务
            logger.debug(f"开始事务: {func.__name__}")
            
            try:
                # 开始事务
                async with db_session.begin():
                    result = await func(*args, **kwargs)
                    logger.debug(f"事务提交成功: {func.__name__}")
                    return result
                    
            except Exception as e:
                logger.error(f"事务执行失败: {func.__name__}, 错误: {str(e)}")
                # 异常会自动触发回滚，不需要手动处理
                raise
        
        return wrapper
    return decorator


def transactional_method(func: Callable) -> Callable:
    """
    简化版事务装饰器，使用默认配置
    等价于 @transactional()
    """
    return transactional()(func)


class TransactionContext:
    """事务上下文管理器，用于手动控制事务"""
    
    def __init__(self, db_session: AsyncSession, 
                 rollback_on_exception: bool = True):
        self.db_session = db_session
        self.rollback_on_exception = rollback_on_exception
        self._transaction = None
    
    async def __aenter__(self):
        """进入事务上下文"""
        if not self.db_session.in_transaction():
            self._transaction = await self.db_session.begin()
            logger.debug("手动开始事务")
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """退出事务上下文"""
        if self._transaction:
            try:
                if exc_type is None:
                    await self._transaction.commit()
                    logger.debug("手动提交事务")
                else:
                    if self.rollback_on_exception:
                        await self._transaction.rollback()
                        logger.debug(f"发生异常，回滚事务: {exc_val}")
            except Exception as e:
                logger.error(f"事务处理异常: {e}")
                raise
    
    async def commit(self):
        """手动提交事务"""
        if self._transaction:
            await self._transaction.commit()
            logger.debug("手动提交事务")
    
    async def rollback(self):
        """手动回滚事务"""
        if self._transaction:
            await self._transaction.rollback()
            logger.debug("手动回滚事务")


# 便捷的事务上下文函数
async def with_transaction(db_session: AsyncSession, 
                          rollback_on_exception: bool = True) -> TransactionContext:
    """
    创建事务上下文管理器
    
    Usage:
        async with with_transaction(db_session) as tx:
            # 业务逻辑
            pass
    """
    return TransactionContext(db_session, rollback_on_exception)
