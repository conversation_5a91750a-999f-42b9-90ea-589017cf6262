from pydantic import BaseModel, Field
from typing import Dict, Any, Optional, TypeVar
from datetime import datetime
from app.schemas.response import StandardResponse




# 与前端JSON结构完全匹配的Pydantic模型

class PhotovoltaicInput(BaseModel):
    """光伏发电输入参数"""
    pv_capacity_mw: float = Field(..., description="光伏装机容量（MW）")
    pv_utilization_hours: float = Field(..., description="光伏利用小时数（h）")
    pv_degradation_y1_percent: float = Field(..., description="首年发电量衰减率（%）")
    pv_degradation_ongoing_percent: float = Field(..., description="年均发电量衰减率（%）")
    system_efficiency_percent: float = Field(..., description="系统效率（%）")
    pv_transmission_distance_km: float = Field(..., description="光伏输电距离（km）")
    equipment_depreciation_years: int = Field(..., description="设备折旧年限（年）")
    grid_price_tax_included_yuan_per_kwh: float = Field(..., description="含税上网电价（元/kWh）")

class WindPowerInput(BaseModel):
    """风力发电输入参数"""
    wind_capacity_mw: float = Field(..., description="风电装机容量（MW）")
    wind_utilization_hours: float = Field(..., description="风电利用小时数（h）")
    system_efficiency_percent: float = Field(..., description="系统效率（%）")
    wind_transmission_distance_km: float = Field(..., description="风电输电距离（km）")
    equipment_depreciation_years: int = Field(..., description="设备折旧年限（年）")
    grid_price_tax_included_yuan_per_kwh: float = Field(..., description="含税上网电价（元/kWh）")

class GridSystemInput(BaseModel):
    """电网系统输入参数"""
    grid_transmission_length_km: float = Field(..., description="电网输电线路长度（km）")
    grid_transmission_unit_cost_wan_yuan_per_km: float = Field(..., description="输电线路单位造价（万元/km）")
    grid_step_up_station_capacity_mva: float = Field(..., description="升压站容量（MVA）")
    grid_step_up_station_unit_cost_wan_yuan_per_mva: float = Field(..., description="升压站单位造价（万元/MVA）")
    grid_step_down_station_capacity_mva: float = Field(..., description="降压站容量（MVA）")
    grid_step_down_station_unit_cost_wan_yuan_per_mva: float = Field(..., description="降压站单位造价（万元/MVA）")

class HydrogenPlantInput(BaseModel):
    """制氢工厂输入参数"""
    h2_plant_capacity_mw: float = Field(..., description="制氢容量（MW）")
    h2_consumption_kwh_per_nm3: float = Field(..., description="单位耗电量（kWh/Nm³）")
    h2_base_consumption_kwh_per_kg: float = Field(..., description="基准单位耗电量（kWh/kg）")
    h2_equipment_service_years: int = Field(..., description="制氢设备使用年限（年）")
    h2_consumption_increase_annual: float = Field(..., description="制氢能耗年增长率")
    h2_water_price_yuan_per_ton: float = Field(..., description="水价（元/吨）")
    h2_wastewater_price_yuan_per_ton: float = Field(..., description="废水处理费（元/吨）")
    h2_staff_count: int = Field(..., description="员工人数")
    h2_staff_salary_wan_yuan_per_year: float = Field(..., description="员工年薪（万元/年）")
    h2_price_transport: float = Field(..., description="交通用氢价格（元/kg）")
    h2_price_chemical: float = Field(..., description="化工用氢价格（元/kg）")
    o2_price_per_ton: float = Field(..., description="氧气价格（元/吨）")
    equipment_depreciation_years: int = Field(..., description="设备折旧年限（年）")
    h2_storage_investment_wan_yuan: float = Field(..., description="储氢投资（万元）")
    h2_storage_capacity_tons: float = Field(..., description="储氢容量（吨）")

class FinancingInput(BaseModel):
    """融资输入参数"""
    loan_term_years: int = Field(..., description="贷款年限（年）")
    loan_interest_rate_percent: float = Field(..., description="贷款利率（%）")
    finance_land_rent_wan_yuan_per_year: float = Field(..., description="土地租金（万元/年）")
    loan_ratio: float = Field(..., description="贷款比例")
    finance_loan_total_wan_yuan: float = Field(..., description="贷款总额（万元）")

class LiquidAmmoniaInput(BaseModel):
    """液氨输入参数"""
    ammonia_price_yuan_per_ton: float = Field(..., description="氨价格（元/吨）")
    ammonia_consumption_tons_per_hour: float = Field(..., description="氨消耗量（吨/小时）")

class EnergyStoragePowerStationInput(BaseModel):
    """储能电站输入参数"""
    electrochemical_energy_storage_scale_mw: float = Field(..., description="电化学储能规模（MW）")
# 计算请求模型
class CalculationRequest(BaseModel):
    """经济测算计算请求"""
    photovoltaic: PhotovoltaicInput
    windPower: WindPowerInput  # 注意：保持与前端字段名一致
    gridSystem: GridSystemInput
    hydrogenPlant: HydrogenPlantInput
    financing: FinancingInput
    liquidAmmonia: LiquidAmmoniaInput
    energyStoragePowerStation: EnergyStoragePowerStationInput
    class Config:
        # 允许通过字段名填充
        populate_by_name = True

# 计算结果模型
class CalculationResult(BaseModel):
    """计算结果 - 纯数据结构，不包含状态信息"""
    result_id: str = Field(..., description="结果ID")
    results: Dict[str, Any] = Field(..., description="计算结果数据")
    timestamp: Optional[datetime] = Field(None, description="计算时间")

# 计算响应模型 - 使用标准响应格式
class CalculationResponse(StandardResponse[CalculationResult]):
    """计算响应 - 标准格式"""

    def __init__(self, data: Optional[CalculationResult] = None, code: int = 200, message: str = ""):
        """
        初始化计算响应

        Args:
            data: 计算结果数据，可以为None
            code: 响应状态码
            message: 响应消息
        """
        super().__init__(data=data, code=code, message=message)