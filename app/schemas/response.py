from pydantic import BaseModel, Field
from typing import Generic, Optional, TypeVar, List



# 通用响应数据类型
T = TypeVar('T')
# 数据继承BaseModel后就已经有__init__等实现了，不用加dataclass注解
class StandardResponse(BaseModel, Generic[T]):
    """标准API响应模型"""
    code: int = Field(..., description="响应状态码")
    message: str = Field(..., description="响应消息")
    data: Optional[T] = Field(None, description="响应数据")
class Page(BaseModel, Generic[T]):
    list:List[T]
    total:int
