# 应用配置
APP_NAME=FastAPI-Template
APP_VERSION=0.1.0
APP_ENV=development  # development, production, testing
APP_DEBUG=true
APP_HOST=0.0.0.0
APP_PORT=8000

# 数据库配置
# 数据库URL，支持不同数据库：
# SQLite: sqlite:///./app.db
# PostgreSQL: postgresql://user:password@localhost:5432/dbname
# MySQL: mysql+pymysql://user:password@localhost:3306/dbname
DATABASE_URL=sqlite:///./app.db
DATABASE_ECHO=true

# JWT配置
JWT_SECRET_KEY=your-secret-key-change-in-production
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30

# 日志配置
LOG_LEVEL=INFO 